APP_NAME=独角数卡
APP_ENV=local
APP_KEY=base64:QOkEfbBK5BieOSshBDJ3Kr6UAC66GTdOjDYg6y6qcBg=
APP_DEBUG=true
APP_URL=http://shop.1rr17gtmp.info

LOG_CHANNEL=stack

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dujiaoka
DB_USERNAME=dujiaoka
DB_PASSWORD=19841020

# redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=
REDIS_PORT=6379
REDIS_DB=2
REDIS_CACHE_DB=3

BROADCAST_DRIVER=log
SESSION_DRIVER=file
SESSION_LIFETIME=120


# 缓存配置
# file为磁盘文件  redis为内存级别
# redis为内存需要安装好redis服务端并配置
CACHE_DRIVER=redis

# 异步消息队列
# sync为同步  redis为异步
# 使用redis异步需要安装好redis服务端并配置
QUEUE_CONNECTION=redis

# 后台语言
## zh_CN 简体中文
## zh_TW 繁体中文
## en    英文
DUJIAO_ADMIN_LANGUAGE=zh_CN

# 后台登录地址
ADMIN_ROUTE_PREFIX=/admin

# 是否开启https (前端开启了后端也必须为true)
# 后台登录出现0err或者其他登录异常问题，大概率是开启了https而后台没有开启，把下面的false改为true即可
ADMIN_HTTPS=true
