-- 简单的转账记录菜单添加脚本
-- 请在数据库管理工具（如phpMyAdmin、Navicat等）中执行

-- 1. 查看当前监控系统菜单
SELECT id, title, uri FROM admin_menu WHERE title = '监控系统' AND parent_id = 0;

-- 2. 如果上面查询没有结果，先执行这个创建监控系统主菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 3. 获取监控系统菜单ID（请将下面的XX替换为实际的ID）
-- 如果监控系统菜单已存在，请查看第1步的查询结果，将XX替换为实际的id
-- 如果是新创建的，通常ID会是一个较大的数字

-- 方法A：如果你知道监控系统菜单的ID，直接替换XX
-- INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
-- VALUES (XX, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW());

-- 方法B：使用子查询自动获取ID（推荐）
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
SELECT id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW()
FROM admin_menu 
WHERE title = '监控系统' AND parent_id = 0 
LIMIT 1;

-- 4. 确保其他监控菜单也存在
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
SELECT id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()
FROM admin_menu 
WHERE title = '监控系统' AND parent_id = 0 
LIMIT 1;

INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
SELECT id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()
FROM admin_menu 
WHERE title = '监控系统' AND parent_id = 0 
LIMIT 1;

INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
SELECT id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW()
FROM admin_menu 
WHERE title = '监控系统' AND parent_id = 0 
LIMIT 1;

-- 5. 查看最终的菜单结构
SELECT 
    m1.title as '主菜单',
    m2.title as '子菜单',
    m2.uri as '路由',
    m2.icon as '图标'
FROM admin_menu m1
LEFT JOIN admin_menu m2 ON m1.id = m2.parent_id
WHERE m1.title = '监控系统' AND m1.parent_id = 0
ORDER BY m2.order;

-- 6. 验证转账记录菜单是否添加成功
SELECT '转账记录菜单检查:' as message;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 转账记录菜单已成功添加'
        ELSE '❌ 转账记录菜单添加失败'
    END as status
FROM admin_menu 
WHERE title = '转账记录' AND uri = 'transfer-records';
