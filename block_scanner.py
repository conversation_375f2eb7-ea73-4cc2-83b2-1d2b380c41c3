#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区块链交易查询脚本
功能：查询指定区块范围内涉及特定钱包地址的交易
使用：python block_scanner.py
"""

import os
import sys
import time
import json
import logging
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class BlockScanner:
    def __init__(self, config_file="config.json"):
        """初始化区块扫描器"""
        self.load_config(config_file)
        self.setup_logging()
        self.setup_proxy()

        self.logger.info("🚀 区块链交易查询器启动")
        self.logger.info(f"🎯 目标地址: {self.target_address}")
        self.logger.info(f"📦 扫描区块范围: {self.start_block} - {self.end_block}")

    def load_config(self, config_file: str):
        """加载配置文件"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                self.api_keys = config.get('api_keys', [])
                self.target_address = config.get('target_address', 'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ')

                scan_range = config.get('scan_range', {})
                self.start_block = scan_range.get('start_block', 74629570)
                self.end_block = scan_range.get('end_block', 74629580)

                self.proxy_config = config.get('proxy', {})
                self.logging_config = config.get('logging', {})
                self.token_decimals_config = config.get('token_decimals', {})

                print(f"✅ 配置文件加载成功: {config_file}")
            else:
                # 使用默认配置
                self.api_keys = []
                self.target_address = "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ"
                self.start_block = 74629570
                self.end_block = 74629580
                self.proxy_config = {"enabled": True, "url": "http://127.0.0.1:7891"}
                self.logging_config = {"level": "INFO", "file": "block_scanner.log"}
                self.token_decimals_config = {}

                print(f"⚠️ 配置文件不存在，使用默认配置: {config_file}")

        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            sys.exit(1)
        
    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.logging_config.get('level', 'INFO').upper())
        log_file = self.logging_config.get('file', 'block_scanner.log')

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_proxy(self):
        """设置代理"""
        # 智能代理检测和配置
        self.proxies = self.detect_and_configure_proxy()
        self.proxy_mode = "代理" if self.proxies.get('http') else "直连"
        self.logger.info(f"🌐 网络模式: {self.proxy_mode}")

    def detect_and_configure_proxy(self) -> dict:
        """智能检测和配置代理"""
        if not self.proxy_config.get('enabled', True):
            self.logger.info("📡 代理已禁用，使用直连模式")
            return {"http": None, "https": None}

        proxy_url = self.proxy_config.get('url', "http://127.0.0.1:7891")

        try:
            # 测试代理连接
            test_proxies = {"http": proxy_url, "https": proxy_url}
            response = requests.get(
                "https://api.trongrid.io/wallet/getnowblock",
                proxies=test_proxies,
                timeout=5,
                verify=False
            )

            if response.status_code == 200:
                self.logger.info(f"✅ 代理服务器可用: {proxy_url}")
                return test_proxies
            else:
                self.logger.warning(f"⚠️ 代理服务器响应异常: {response.status_code}")
                return {"http": None, "https": None}

        except Exception as e:
            self.logger.info(f"📡 代理不可用，使用直连模式: {e}")
            return {"http": None, "https": None}

    def get_random_api_key(self) -> Optional[str]:
        """随机选择一个API Key"""
        if not self.api_keys:
            return None
        return random.choice(self.api_keys)

    def add_random_delay(self, min_seconds: float = 0.1, max_seconds: float = 0.3):
        """添加随机延迟，避免请求过于频繁"""
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.debug(f"⏱️ 随机延迟 {delay:.2f} 秒...")
        time.sleep(delay)

    def get_token_decimals(self, contract_address: str) -> int:
        """获取TRC20代币的小数位数"""
        try:
            # 先检查配置文件中的设置
            if contract_address in self.token_decimals_config:
                decimals = self.token_decimals_config[contract_address]
                self.logger.debug(f"📊 从配置文件获取小数位数: {contract_address} = {decimals}")
                return decimals

            # 常见代币的小数位数映射（扩展版本）
            known_decimals = {
                'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t': 6,   # USDT
                'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8': 6,   # USDC
                'TLf2b2g4SNTpkHPXvNF8tbAb6Uu6Ahn36h': 18,  # BTT
                'TSSMHYeV2uE9qYH95DqyoCuNCzEL1NvU3S': 18,  # SUN
                'TKfjV9RNKJJCqPvBtK8L7Knykh7DNWvnYt': 18,  # WTRX
                '41a614f803b6fd780986a42c78ec9c7f77e6ded13c': 6,  # 根据你的交易，这个代币可能是6位小数
                # 添加更多常见代币
                '4199469fd5aa08cfe836395ce993c0ebd567e2c14f': 6,   # 常见稳定币格式
                '4139dd12a54e2bab7c82aa14a1e158b34263d2d510': 6,   # 常见稳定币格式
                '41c60a6f5c81431c97ed01b61698b6853557f3afd4': 6,   # 常见稳定币格式
            }

            # 检查已知代币
            if contract_address in known_decimals:
                self.logger.debug(f"📊 从已知代币映射获取小数位数: {contract_address} = {known_decimals[contract_address]}")
                return known_decimals[contract_address]

            # 对于未知代币，不进行API查询，直接使用智能默认值
            # 根据合约地址特征推测小数位数
            if len(contract_address) == 42 and contract_address.startswith('41'):
                # 大多数TRON上的稳定币使用6位小数
                self.logger.debug(f"📊 未知代币使用默认6位小数: {contract_address}")
                return 6
            else:
                # 其他代币使用18位小数
                self.logger.debug(f"📊 未知代币使用默认18位小数: {contract_address}")
                return 18

        except Exception as e:
            self.logger.error(f"❌ 获取代币小数位数失败: {e}")
            return 6  # 默认6位小数

    def get_block_transactions(self, block_number: int, api_key: str) -> List[Dict]:
        """获取指定区块的所有交易"""
        try:
            url = "https://api.trongrid.io/wallet/getblockbynum"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"num": block_number}

            response = requests.post(url, json=data, headers=headers, proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                transactions = result.get('transactions', [])
                self.logger.info(f"📦 区块 {block_number} 包含 {len(transactions)} 个交易")
                return transactions
            else:
                self.logger.warning(f"⚠️ 获取区块交易失败: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"❌ 获取区块交易失败: {e}")
            return []

    def hex_to_tron_address(self, hex_address: str) -> str:
        """将十六进制地址转换为TRON地址"""
        try:
            # 清理十六进制地址
            clean_hex = hex_address.strip().lower()
            if clean_hex.startswith('0x'):
                clean_hex = clean_hex[2:]

            # 处理不同长度的十六进制地址
            if len(clean_hex) == 64:
                # 64字符地址：去掉前24个字符的零填充，保留后40字符
                clean_hex = clean_hex[24:]
                self.logger.debug(f"🔧 处理64字符地址，提取后40字符: {clean_hex}")
            elif len(clean_hex) == 40:
                # 40字符地址：直接使用
                self.logger.debug(f"🔧 处理40字符地址: {clean_hex}")
            else:
                self.logger.warning(f"⚠️ 十六进制地址长度异常: {len(clean_hex)}, 地址: {clean_hex}")
                return ""

            # 添加TRON地址前缀41
            tron_hex = "41" + clean_hex

            # 转换为Base58格式
            tron_address = self.hex_to_base58(tron_hex)

            self.logger.debug(f"🔄 地址转换: {hex_address} -> {clean_hex} -> {tron_address}")
            return tron_address

        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            return ""

    def hex_to_base58(self, hex_str: str) -> str:
        """将十六进制字符串转换为Base58地址"""
        try:
            import hashlib

            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换十六进制为字节
            hex_bytes = bytes.fromhex(hex_str)

            # 计算双重SHA256校验和
            hash1 = hashlib.sha256(hex_bytes).digest()
            hash2 = hashlib.sha256(hash1).digest()
            checksum = hash2[:4]

            # 添加校验和
            full_bytes = hex_bytes + checksum

            # 转换为大整数
            num = int.from_bytes(full_bytes, 'big')

            # 转换为Base58
            encoded = ""
            while num > 0:
                num, remainder = divmod(num, 58)
                encoded = alphabet[remainder] + encoded

            # 处理前导零
            for byte in full_bytes:
                if byte == 0:
                    encoded = alphabet[0] + encoded
                else:
                    break

            return encoded

        except Exception as e:
            self.logger.error(f"❌ Base58编码失败: {e}")
            return ""

    def analyze_transaction(self, tx: Dict) -> Optional[Dict]:
        """分析交易是否涉及目标地址"""
        try:
            tx_hash = tx.get('txID', '')
            found_transactions = []

            # 检查TRC20转账（USDT等代币）
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TriggerSmartContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        # 检查合约地址
                        contract_address = value.get('contract_address', '')
                        
                        # 解析转账数据
                        data = value.get('data', '')
                        if data and len(data) >= 136:  # transfer方法的数据长度
                            # 提取方法签名、to地址和金额
                            method_signature = data[0:8]   # 方法签名 (a9059cbb)
                            to_address_hex = data[8:72]    # 接收地址 (64字符，前24位补零)
                            amount_hex = data[72:136]      # 转账金额 (64字符十六进制)

                            try:
                                # 转换地址格式
                                to_address = self.hex_to_tron_address(to_address_hex)

                                # 获取代币小数位数和类型
                                decimals = self.get_token_decimals(contract_address)
                                raw_amount = int(amount_hex, 16)
                                amount = raw_amount / (10 ** decimals)

                                # 确定代币类型
                                if contract_address == 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t':
                                    token_type = 'USDT'
                                elif contract_address == 'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8':
                                    token_type = 'USDC'
                                else:
                                    token_type = 'TRC20'

                                # 记录调试信息
                                self.logger.debug(f"🔍 代币转账解析: 合约={contract_address}, 方法签名={method_signature}, 原始金额={raw_amount}, 小数位={decimals}, 最终金额={amount}")

                                # 验证方法签名（transfer方法的签名应该是a9059cbb）
                                if method_signature.lower() != 'a9059cbb':
                                    self.logger.warning(f"⚠️ 非标准transfer方法签名: {method_signature}, 可能解析错误")

                                # 检查是否涉及目标地址
                                if to_address == self.target_address:
                                    found_transactions.append({
                                        'tx_hash': tx_hash,
                                        'type': f'{token_type}_TRANSFER',
                                        'to_address': to_address,
                                        'amount': amount,
                                        'raw_amount': raw_amount,
                                        'decimals': decimals,
                                        'contract': contract_address,
                                        'direction': 'IN'  # 接收
                                    })

                                # 检查from地址（需要从owner_address获取）
                                from_address = value.get('owner_address', '')
                                if from_address == self.target_address:
                                    found_transactions.append({
                                        'tx_hash': tx_hash,
                                        'type': f'{token_type}_TRANSFER',
                                        'from_address': from_address,
                                        'to_address': to_address,
                                        'amount': amount,
                                        'raw_amount': raw_amount,
                                        'decimals': decimals,
                                        'contract': contract_address,
                                        'direction': 'OUT'  # 发送
                                    })

                            except Exception as e:
                                self.logger.debug(f"解析TRC20转账失败: {e}")

            # 检查TRX转账
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TransferContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        from_address = value.get('owner_address', '')
                        to_address = value.get('to_address', '')
                        amount = value.get('amount', 0) / 1000000  # TRX有6位小数

                        # 检查是否涉及目标地址
                        if to_address == self.target_address:
                            found_transactions.append({
                                'tx_hash': tx_hash,
                                'type': 'TRX_TRANSFER',
                                'from_address': from_address,
                                'to_address': to_address,
                                'amount': amount,
                                'direction': 'IN'  # 接收
                            })
                        elif from_address == self.target_address:
                            found_transactions.append({
                                'tx_hash': tx_hash,
                                'type': 'TRX_TRANSFER',
                                'from_address': from_address,
                                'to_address': to_address,
                                'amount': amount,
                                'direction': 'OUT'  # 发送
                            })

            return found_transactions if found_transactions else None

        except Exception as e:
            self.logger.error(f"❌ 分析交易失败: {e}")
            return None

    def scan_blocks(self) -> List[Dict]:
        """扫描指定区块范围查找涉及目标地址的交易"""
        try:
            self.logger.info(f"🔍 开始扫描区块 {self.start_block} 到 {self.end_block}")
            self.logger.info(f"🎯 目标地址: {self.target_address}")

            found_transactions = []
            total_transactions_scanned = 0

            # 扫描每个区块
            for block_num in range(self.start_block, self.end_block + 1):
                try:
                    # 随机选择API Key
                    api_key = self.get_random_api_key()
                    if not api_key:
                        self.logger.error("❌ 没有可用的API Key")
                        break

                    # 获取区块交易
                    transactions = self.get_block_transactions(block_num, api_key)
                    total_transactions_scanned += len(transactions)

                    # 分析每个交易
                    for tx in transactions:
                        results = self.analyze_transaction(tx)
                        if results:
                            for result in results:
                                result['block_number'] = block_num
                                result['block_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                found_transactions.append(result)
                                
                                direction_emoji = "📥" if result['direction'] == 'IN' else "📤"
                                self.logger.info(f"🎯 {direction_emoji} 发现相关交易: {result['type']} {result['amount']} (区块: {block_num})")

                    # 添加小延迟避免API限制
                    self.add_random_delay(0.1, 0.2)

                except Exception as e:
                    self.logger.error(f"❌ 扫描区块 {block_num} 失败: {e}")
                    continue

            self.logger.info(f"✅ 区块扫描完成:")
            self.logger.info(f"   - 扫描区块数: {self.end_block - self.start_block + 1}")
            self.logger.info(f"   - 总交易数: {total_transactions_scanned}")
            self.logger.info(f"   - 发现相关交易: {len(found_transactions)} 个")
            
            return found_transactions

        except Exception as e:
            self.logger.error(f"❌ 区块链扫描失败: {e}")
            return []

    def save_results(self, transactions: List[Dict]):
        """保存扫描结果到文件"""
        try:
            if not transactions:
                self.logger.info("📭 没有找到相关交易")
                return

            # 保存为JSON文件
            filename = f"scan_results_{self.start_block}_{self.end_block}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(transactions, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"💾 扫描结果已保存到: {filename}")

            # 打印详细结果
            self.logger.info("📋 详细交易信息:")
            for i, tx in enumerate(transactions, 1):
                direction_emoji = "📥" if tx['direction'] == 'IN' else "📤"
                self.logger.info(f"  {i}. {direction_emoji} {tx['type']}")
                self.logger.info(f"     交易哈希: {tx['tx_hash']}")
                self.logger.info(f"     区块号: {tx['block_number']}")
                self.logger.info(f"     金额: {tx['amount']}")
                if 'raw_amount' in tx and 'decimals' in tx:
                    self.logger.info(f"     原始金额: {tx['raw_amount']} (小数位: {tx['decimals']})")
                if 'from_address' in tx:
                    self.logger.info(f"     发送方: {tx['from_address']}")
                if 'to_address' in tx:
                    self.logger.info(f"     接收方: {tx['to_address']}")
                if 'contract' in tx:
                    self.logger.info(f"     合约地址: {tx['contract']}")
                self.logger.info("")

        except Exception as e:
            self.logger.error(f"❌ 保存结果失败: {e}")

    def run(self):
        """运行扫描"""
        try:
            # 检查API Keys配置
            if not self.api_keys or any("你的" in key for key in self.api_keys):
                self.logger.error("❌ 请先在config.json中配置TronGrid API Keys")
                self.logger.error("   获取API Key: https://www.trongrid.io/")
                return

            # 执行扫描
            transactions = self.scan_blocks()

            # 保存结果
            self.save_results(transactions)

        except Exception as e:
            self.logger.error(f"❌ 运行失败: {e}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='TRON区块链交易查询工具')
    parser.add_argument('--config', '-c', default='config.json', help='配置文件路径')
    parser.add_argument('--address', '-a', help='目标钱包地址')
    parser.add_argument('--start', '-s', type=int, help='起始区块号')
    parser.add_argument('--end', '-e', type=int, help='结束区块号')

    args = parser.parse_args()

    # 创建扫描器
    scanner = BlockScanner(args.config)

    # 命令行参数覆盖配置文件
    if args.address:
        scanner.target_address = args.address
    if args.start:
        scanner.start_block = args.start
    if args.end:
        scanner.end_block = args.end

    # 运行扫描
    scanner.run()

if __name__ == "__main__":
    main()
