-- 检查当前数据库结构
-- 请在数据库管理工具中执行此脚本，查看现有结构

-- 1. 查看所有表
SELECT '=== 当前数据库中的所有表 ===' as info;
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表说明',
    TABLE_ROWS as '记录数',
    CREATE_TIME as '创建时间'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME;

-- 2. 查看admin_menu表结构
SELECT '=== admin_menu表结构 ===' as info;
SHOW COLUMNS FROM admin_menu;

-- 3. 查看现有的菜单数据
SELECT '=== 现有菜单结构 ===' as info;
SELECT
    id,
    parent_id,
    `order`,
    title as '菜单标题',
    icon as '图标',
    uri as '路由'
FROM admin_menu
ORDER BY parent_id, `order`;

-- 4. 查看菜单层级结构
SELECT '=== 菜单层级结构 ===' as info;
SELECT
    CASE
        WHEN parent_id = 0 THEN CONCAT('📁 ', title)
        ELSE CONCAT('  📄 ', title, ' (', uri, ')')
    END as '菜单结构'
FROM admin_menu
ORDER BY parent_id, `order`;

-- 5. 检查是否已存在监控相关菜单
SELECT '=== 监控相关菜单检查 ===' as info;
SELECT
    id,
    parent_id,
    title,
    uri
FROM admin_menu
WHERE title LIKE '%监控%'
   OR title LIKE '%授权%'
   OR title LIKE '%转账%'
   OR uri LIKE '%monitor%'
   OR uri LIKE '%authorization%'
   OR uri LIKE '%transfer%';

-- 6. 检查是否已存在相关表
SELECT '=== 相关表检查 ===' as info;
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '说明'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
AND (TABLE_NAME LIKE '%authorization%' 
     OR TABLE_NAME LIKE '%transfer%'
     OR TABLE_NAME LIKE '%monitor%'
     OR TABLE_NAME LIKE '%fish%');

-- 7. 查看migrations表（如果存在）
SELECT '=== 迁移记录检查 ===' as info;
SELECT migration, batch
FROM migrations
WHERE migration LIKE '%authorization%'
   OR migration LIKE '%transfer%'
   OR migration LIKE '%monitor%'
ORDER BY batch, migration;

-- 8. 检查options表中的配置
SELECT '=== 系统配置检查 ===' as info;
SELECT name, value
FROM options
WHERE name LIKE '%address%'
   OR name LIKE '%key%'
   OR name LIKE '%threshold%'
   OR name LIKE '%monitor%'
ORDER BY name;

-- 9. 检查fish表结构（如果存在）
SELECT '=== fish表检查 ===' as info;
SELECT 
    COLUMN_NAME as '字段名',
    COLUMN_TYPE as '类型',
    IS_NULLABLE as '可空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'fish'
ORDER BY ORDINAL_POSITION;

-- 10. 总结信息
SELECT '=== 检查总结 ===' as info;
SELECT
    (SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE()) as '总表数',
    (SELECT COUNT(*) FROM admin_menu) as '总菜单数',
    (SELECT COUNT(*) FROM admin_menu WHERE parent_id = 0) as '主菜单数',
    (SELECT COUNT(*) FROM admin_menu WHERE parent_id > 0) as '子菜单数';

-- 使用说明：
-- 执行此脚本后，请将结果发送给我，我会根据你的实际数据库结构
-- 来修改 create_transfer_system_tables.sql 脚本，确保：
-- 1. 不会与现有表冲突
-- 2. 菜单结构合理
-- 3. 兼容现有系统
