<?php
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== 检查admin_menu表中的监控相关菜单 ===\n";

try {
    $menus = DB::table('admin_menu')
        ->select('id', 'parent_id', 'title', 'uri', 'icon', 'show')
        ->where(function($query) {
            $query->where('title', 'like', '%监控%')
                   ->orWhere('title', 'like', '%转账%')
                   ->orWhere('uri', 'like', '%transfer%');
        })
        ->orderBy('parent_id')
        ->orderBy('order')
        ->get();

    if ($menus->isEmpty()) {
        echo "❌ 没有找到监控系统或转账记录相关的菜单\n";
        echo "需要添加转账记录菜单到数据库\n";
    } else {
        echo "✅ 找到以下菜单:\n";
        foreach ($menus as $menu) {
            echo "ID: {$menu->id}, 父ID: {$menu->parent_id}, 标题: {$menu->title}, URI: {$menu->uri}, 显示: {$menu->show}\n";
        }
    }

    // 检查transfer_records表是否存在
    echo "\n=== 检查transfer_records表 ===\n";
    $tables = DB::select("SHOW TABLES LIKE 'transfer_records'");
    if (empty($tables)) {
        echo "❌ transfer_records表不存在\n";
    } else {
        echo "✅ transfer_records表存在\n";
    }

    // 检查路由是否正确配置
    echo "\n=== 检查路由配置 ===\n";
    if (file_exists('app/Admin/routes.php')) {
        $routeContent = file_get_contents('app/Admin/routes.php');
        if (strpos($routeContent, 'transfer-records') !== false) {
            echo "✅ 路由配置正确\n";
        } else {
            echo "❌ 路由配置缺失\n";
        }
    } else {
        echo "❌ 路由文件不存在\n";
    }

    // 检查控制器是否存在
    echo "\n=== 检查控制器 ===\n";
    if (file_exists('app/Admin/Controllers/TransferRecordController.php')) {
        echo "✅ TransferRecordController控制器存在\n";
    } else {
        echo "❌ TransferRecordController控制器不存在\n";
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
