-- 清理重复的监控系统菜单
-- 保留一个完整的监控系统菜单，删除其他重复项

-- 1. 查看当前所有监控相关菜单
SELECT '=== 当前监控相关菜单 ===' as message;
SELECT 
    id,
    parent_id,
    title,
    uri,
    icon,
    `order`
FROM admin_menu 
WHERE title LIKE '%监控%' 
   OR title LIKE '%授权%' 
   OR title LIKE '%转账%'
   OR uri LIKE '%monitor%'
   OR uri LIKE '%authorization%'
   OR uri LIKE '%transfer%'
ORDER BY parent_id, id;

-- 2. 找出重复的监控系统主菜单
SELECT '=== 重复的监控系统主菜单 ===' as message;
SELECT 
    id,
    title,
    `order`,
    created_at
FROM admin_menu 
WHERE title = '监控系统' AND parent_id = 0
ORDER BY id;

-- 3. 删除重复的监控系统菜单（保留ID最小的）
-- 首先删除重复主菜单的子菜单
DELETE FROM admin_menu 
WHERE parent_id IN (
    SELECT id FROM (
        SELECT id 
        FROM admin_menu 
        WHERE title = '监控系统' AND parent_id = 0 
        AND id NOT IN (
            SELECT MIN(id) 
            FROM admin_menu 
            WHERE title = '监控系统' AND parent_id = 0
        )
    ) AS duplicate_parents
);

-- 删除重复的监控系统主菜单（保留ID最小的）
DELETE FROM admin_menu 
WHERE title = '监控系统' 
AND parent_id = 0 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM admin_menu 
        WHERE title = '监控系统' AND parent_id = 0
    ) AS keep_menu
);

-- 4. 获取保留的监控系统菜单ID
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 5. 删除重复的子菜单，只保留每种类型的一个
-- 删除重复的监控仪表板
DELETE FROM admin_menu 
WHERE title = '监控仪表板' 
AND parent_id = @monitoring_menu_id 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM admin_menu 
        WHERE title = '监控仪表板' AND parent_id = @monitoring_menu_id
    ) AS keep_dashboard
);

-- 删除重复的授权记录
DELETE FROM admin_menu 
WHERE title = '授权记录' 
AND parent_id = @monitoring_menu_id 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM admin_menu 
        WHERE title = '授权记录' AND parent_id = @monitoring_menu_id
    ) AS keep_auth
);

-- 删除重复的授权地址监控
DELETE FROM admin_menu 
WHERE title = '授权地址监控' 
AND parent_id = @monitoring_menu_id 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM admin_menu 
        WHERE title = '授权地址监控' AND parent_id = @monitoring_menu_id
    ) AS keep_addr
);

-- 删除重复的转账记录
DELETE FROM admin_menu 
WHERE title = '转账记录' 
AND parent_id = @monitoring_menu_id 
AND id NOT IN (
    SELECT * FROM (
        SELECT MIN(id) 
        FROM admin_menu 
        WHERE title = '转账记录' AND parent_id = @monitoring_menu_id
    ) AS keep_transfer
);

-- 6. 确保子菜单的order正确
UPDATE admin_menu SET `order` = 1 WHERE title = '监控仪表板' AND parent_id = @monitoring_menu_id;
UPDATE admin_menu SET `order` = 2 WHERE title = '授权记录' AND parent_id = @monitoring_menu_id;
UPDATE admin_menu SET `order` = 3 WHERE title = '授权地址监控' AND parent_id = @monitoring_menu_id;
UPDATE admin_menu SET `order` = 4 WHERE title = '转账记录' AND parent_id = @monitoring_menu_id;

-- 7. 验证清理结果
SELECT '=== 清理后的监控系统菜单 ===' as message;
SELECT 
    m1.id as '主菜单ID',
    m1.title as '主菜单',
    m1.order as '主菜单排序',
    m2.id as '子菜单ID',
    m2.title as '子菜单',
    m2.uri as '路由',
    m2.order as '子菜单排序'
FROM admin_menu m1
LEFT JOIN admin_menu m2 ON m1.id = m2.parent_id
WHERE m1.title = '监控系统' AND m1.parent_id = 0
ORDER BY m2.order;

-- 8. 检查是否还有重复
SELECT '=== 重复检查 ===' as message;
SELECT 
    title,
    COUNT(*) as '数量'
FROM admin_menu 
WHERE title IN ('监控系统', '监控仪表板', '授权记录', '授权地址监控', '转账记录')
GROUP BY title
HAVING COUNT(*) > 1;

SELECT '🎉 菜单清理完成！' as message;
SELECT '💡 请刷新后台页面查看清理结果' as next_step;
