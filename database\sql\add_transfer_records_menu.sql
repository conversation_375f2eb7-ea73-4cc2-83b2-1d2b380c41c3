-- 添加转账记录管理菜单
-- 此脚本为转账记录功能添加后台菜单

-- 1. 检查是否已存在监控系统菜单
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 2. 如果监控系统菜单不存在，则创建
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 3. 重新获取监控系统菜单ID
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' AND parent_id = 0 LIMIT 1);

-- 4. 删除可能存在的重复转账记录菜单
DELETE FROM `admin_menu` WHERE `title` = '转账记录' AND `parent_id` = @monitoring_menu_id;

-- 5. 添加转账记录子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) 
VALUES (@monitoring_menu_id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW());

-- 6. 确保其他监控系统子菜单存在
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 7. 显示安装结果
SELECT '转账记录菜单安装完成！' as message;

-- 8. 查看监控系统菜单结构
SELECT 
    id,
    parent_id,
    `order`,
    title,
    icon,
    uri,
    `show` as visible
FROM admin_menu 
WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id
ORDER BY parent_id, `order`;

-- 9. 验证菜单层级结构
SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('📁 ', title)
        ELSE CONCAT('  📄 ', title)
    END as menu_structure,
    uri as route,
    icon
FROM admin_menu 
WHERE id = @monitoring_menu_id OR parent_id = @monitoring_menu_id
ORDER BY parent_id, `order`;

-- 10. 菜单说明
/*
添加的菜单结构：
📁 监控系统 (fa-eye)
  📄 监控仪表板 (fa-dashboard) -> monitor-dashboard
  📄 授权记录 (fa-list) -> authorizations  
  📄 授权地址监控 (fa-desktop) -> authorized-addresses
  📄 转账记录 (fa-exchange) -> transfer-records  ← 新增

图标说明：
- fa-eye: 眼睛图标，象征监控功能
- fa-exchange: 交换图标，象征转账操作
- fa-dashboard: 仪表板图标
- fa-list: 列表图标
- fa-desktop: 桌面图标

这些都是FontAwesome图标，与dujiaoka项目兼容
*/
