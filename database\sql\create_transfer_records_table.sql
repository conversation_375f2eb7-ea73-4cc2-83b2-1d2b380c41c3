-- 创建转账记录表
CREATE TABLE IF NOT EXISTS `transfer_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户钱包地址',
  `from_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '转出地址',
  `to_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '转入地址',
  `amount` decimal(16,6) NOT NULL COMMENT '转账金额',
  `tx_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易哈希',
  `contract_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' COMMENT '合约地址',
  `transfer_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'transferFrom' COMMENT '转账类型',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0处理中 1成功 2失败',
  `gas_fee` decimal(16,6) DEFAULT NULL COMMENT '矿工费',
  `block_number` bigint(20) DEFAULT NULL COMMENT '区块号',
  `confirmation_count` int(11) DEFAULT '0' COMMENT '确认数',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `triggered_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'auto_monitor' COMMENT '触发方式',
  `balance_before` decimal(16,6) DEFAULT NULL COMMENT '转账前余额',
  `balance_after` decimal(16,6) DEFAULT '0.000000' COMMENT '转账后余额',
  `threshold_value` decimal(16,6) DEFAULT NULL COMMENT '触发阈值',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_address` (`user_address`),
  KEY `idx_tx_hash` (`tx_hash`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_from_to` (`from_address`, `to_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转账记录表';

-- 创建转账记录的迁移文件对应的表结构
-- 这个表用于记录所有的授权转账操作，包括成功和失败的记录

-- 插入示例数据（可选）
-- INSERT INTO `transfer_records` (`user_address`, `from_address`, `to_address`, `amount`, `tx_hash`, `status`, `triggered_by`, `balance_before`, `balance_after`, `threshold_value`, `created_at`, `updated_at`) VALUES
-- ('TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ', 'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ', 'TReceiveAddress123456789', 15.500000, '0x1234567890abcdef', 1, 'auto_monitor', 15.500000, 0.000000, 10.000000, NOW(), NOW());

-- 查看表结构
DESCRIBE transfer_records;

-- 显示转账记录统计
SELECT 
    COUNT(*) as '总转账次数',
    COUNT(CASE WHEN status = 1 THEN 1 END) as '成功次数',
    COUNT(CASE WHEN status = 2 THEN 1 END) as '失败次数',
    COUNT(CASE WHEN status = 0 THEN 1 END) as '处理中',
    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as '成功转账总额',
    AVG(CASE WHEN status = 1 THEN amount END) as '平均转账金额'
FROM transfer_records;

-- 显示最近的转账记录
SELECT 
    user_address as '用户地址',
    amount as '转账金额',
    CASE status 
        WHEN 0 THEN '处理中'
        WHEN 1 THEN '成功'
        WHEN 2 THEN '失败'
    END as '状态',
    tx_hash as '交易哈希',
    triggered_by as '触发方式',
    created_at as '创建时间'
FROM transfer_records 
ORDER BY created_at DESC 
LIMIT 10;
