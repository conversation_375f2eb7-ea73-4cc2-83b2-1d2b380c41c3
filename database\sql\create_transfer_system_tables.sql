-- 适配现有数据库的转账记录系统
-- 基于实际数据库结构定制，避免冲突

-- 检查现有表状态
SELECT '=== 检查现有表状态 ===' as message;

-- 检查authorizations表
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✅ authorizations表已存在'
        ELSE '❌ authorizations表不存在'
    END as 'authorizations表状态'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'authorizations';

-- 检查authorized_addresses表
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✅ authorized_addresses表已存在'
        ELSE '❌ authorized_addresses表不存在'
    END as 'authorized_addresses表状态'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'authorized_addresses';

-- 检查transfer_records表
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✅ transfer_records表已存在'
        ELSE '❌ transfer_records表不存在'
    END as 'transfer_records表状态'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'transfer_records';

-- 由于表已存在，只需要确保表结构正确
-- 如果需要修改表结构，请手动执行以下ALTER语句

-- 1. 确保authorizations表结构正确（如果需要）
-- ALTER TABLE authorizations ADD COLUMN IF NOT EXISTS verified_at timestamp NULL DEFAULT NULL COMMENT '验证时间';

-- 2. 确保authorized_addresses表结构正确（如果需要）
-- ALTER TABLE authorized_addresses ADD COLUMN IF NOT EXISTS total_collected decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '总收集金额';

-- 3. 确保transfer_records表结构正确（如果需要）
-- ALTER TABLE transfer_records ADD COLUMN IF NOT EXISTS triggered_by varchar(50) NOT NULL DEFAULT 'auto_monitor' COMMENT '触发方式';

-- 4. 添加转账记录菜单
-- 检查是否已有监控系统菜单
SELECT '=== 检查监控系统菜单 ===' as message;

SELECT
    id,
    title,
    uri,
    icon
FROM admin_menu
WHERE title = '监控系统' AND parent_id = 0;

-- 如果上面查询有结果，说明监控系统菜单已存在
-- 如果没有结果，执行下面的创建语句

-- 创建监控系统主菜单（如果不存在）
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`)
VALUES (0, 60, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

-- 添加转账记录子菜单
-- 使用子查询获取监控系统菜单ID
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`)
SELECT id, 4, '转账记录', 'fa-exchange', 'transfer-records', '', 1, NOW(), NOW()
FROM admin_menu
WHERE title = '监控系统' AND parent_id = 0
LIMIT 1;

-- 确保其他监控子菜单存在
INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`)
SELECT id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()
FROM admin_menu
WHERE title = '监控系统' AND parent_id = 0
LIMIT 1;

INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`)
SELECT id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()
FROM admin_menu
WHERE title = '监控系统' AND parent_id = 0
LIMIT 1;

INSERT IGNORE INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`)
SELECT id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW()
FROM admin_menu
WHERE title = '监控系统' AND parent_id = 0
LIMIT 1;

-- 5. 插入迁移记录（避免Laravel再次尝试迁移）
INSERT IGNORE INTO `migrations` (`migration`, `batch`) VALUES
('2024_01_01_000000_create_authorizations_table', 1),
('2024_01_02_000000_create_authorized_addresses_table', 1),
('2024_01_03_000000_create_transfer_records_table', 1);

-- 6. 验证菜单添加结果
SELECT '=== 菜单添加结果验证 ===' as message;

-- 查看监控系统菜单结构
SELECT
    m1.id as '主菜单ID',
    m1.title as '主菜单',
    m2.id as '子菜单ID',
    m2.title as '子菜单',
    m2.uri as '路由',
    m2.icon as '图标',
    m2.order as '排序'
FROM admin_menu m1
LEFT JOIN admin_menu m2 ON m1.id = m2.parent_id
WHERE m1.title = '监控系统' AND m1.parent_id = 0
ORDER BY m2.order;

-- 7. 检查转账记录菜单是否添加成功
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✅ 转账记录菜单添加成功'
        ELSE '❌ 转账记录菜单添加失败'
    END as '转账记录菜单状态'
FROM admin_menu
WHERE title = '转账记录' AND uri = 'transfer-records';

-- 8. 验证表状态
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '说明',
    TABLE_ROWS as '记录数'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('authorizations', 'authorized_addresses', 'transfer_records');

-- 9. 完成提示
SELECT '🎉 转账记录系统配置完成！' as message;
SELECT '💡 请刷新后台页面查看"监控系统 → 转账记录"菜单' as next_step;
