#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试交易解析工具
用于分析特定交易的数据解析问题
"""

import json
import requests

def debug_transaction():
    """调试特定交易"""
    
    # 你的交易哈希
    tx_hash = "4b1eb64be6064246ddc82f4fc4589a0da577d1e9f94f05bb5c7e65525549eacf"
    
    # API配置
    api_key = "d8794150-ec22-4e48-842d-ba26aad66a6d"  # 从config.json中获取
    
    print(f"🔍 调试交易: {tx_hash}")
    print("=" * 80)
    
    # 通过TronGrid API获取交易详情
    try:
        url = f"https://api.trongrid.io/v1/transactions/{tx_hash}"
        headers = {"TRON-PRO-API-KEY": api_key}
        
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            tx_data = response.json()
            
            print("📋 交易原始数据:")
            print(json.dumps(tx_data, indent=2, ensure_ascii=False))
            
            # 分析TRC20转账数据
            if 'data' in tx_data and len(tx_data['data']) > 0:
                tx = tx_data['data'][0]
                
                print("\n🔍 分析TRC20转账:")
                
                if 'raw_data' in tx and 'contract' in tx['raw_data']:
                    for i, contract in enumerate(tx['raw_data']['contract']):
                        print(f"\n合约 {i+1}:")
                        print(f"  类型: {contract.get('type')}")
                        
                        if contract.get('type') == 'TriggerSmartContract':
                            parameter = contract.get('parameter', {})
                            value = parameter.get('value', {})
                            
                            contract_address = value.get('contract_address', '')
                            data = value.get('data', '')
                            owner_address = value.get('owner_address', '')
                            
                            print(f"  合约地址: {contract_address}")
                            print(f"  发送方: {owner_address}")
                            print(f"  数据长度: {len(data)}")
                            print(f"  原始数据: {data}")
                            
                            if data and len(data) >= 136:
                                # 解析transfer方法
                                method_signature = data[:8]
                                to_address_hex = data[8:72]  # 修正：从第8位开始，不是第32位
                                amount_hex = data[72:136]
                                
                                print(f"\n  📊 数据解析:")
                                print(f"    方法签名: {method_signature}")
                                print(f"    接收地址(hex): {to_address_hex}")
                                print(f"    金额(hex): {amount_hex}")
                                
                                # 转换金额
                                try:
                                    raw_amount = int(amount_hex, 16)
                                    print(f"    原始金额: {raw_amount}")
                                    
                                    # 不同小数位数的结果
                                    for decimals in [0, 6, 8, 18]:
                                        amount = raw_amount / (10 ** decimals)
                                        print(f"    {decimals}位小数: {amount}")
                                        
                                        # 检查哪个结果最合理（接近4 USDT）
                                        if 3 <= amount <= 5:
                                            print(f"    ✅ {decimals}位小数结果最合理: {amount}")
                                    
                                except Exception as e:
                                    print(f"    ❌ 金额解析失败: {e}")
                                
                                # 转换地址
                                try:
                                    to_address = hex_to_tron_address(to_address_hex)
                                    print(f"    接收地址: {to_address}")
                                except Exception as e:
                                    print(f"    ❌ 地址转换失败: {e}")
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def hex_to_tron_address(hex_address):
    """将十六进制地址转换为TRON地址"""
    try:
        import hashlib
        
        # 清理十六进制地址
        clean_hex = hex_address.strip().lower()
        if clean_hex.startswith('0x'):
            clean_hex = clean_hex[2:]
        
        # 处理不同长度的十六进制地址
        if len(clean_hex) == 64:
            # 64字符地址：去掉前24个字符的零填充，保留后40字符
            clean_hex = clean_hex[24:]
        elif len(clean_hex) != 40:
            return f"❌ 地址长度异常: {len(clean_hex)}"
        
        # 添加TRON地址前缀41
        tron_hex = "41" + clean_hex
        
        # 转换为Base58格式
        alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
        hex_bytes = bytes.fromhex(tron_hex)
        hash1 = hashlib.sha256(hex_bytes).digest()
        hash2 = hashlib.sha256(hash1).digest()
        checksum = hash2[:4]
        full_bytes = hex_bytes + checksum
        num = int.from_bytes(full_bytes, 'big')
        encoded = ""
        while num > 0:
            num, remainder = divmod(num, 58)
            encoded = alphabet[remainder] + encoded
        for byte in full_bytes:
            if byte == 0:
                encoded = alphabet[0] + encoded
            else:
                break
        
        return encoded
        
    except Exception as e:
        return f"❌ 转换失败: {e}"

def analyze_transfer_data():
    """分析transfer方法的数据结构"""
    print("\n📚 TRC20 transfer方法数据结构:")
    print("=" * 50)
    print("标准的transfer(address,uint256)方法数据结构:")
    print("  0-8位:   方法签名 (a9059cbb)")
    print("  8-72位:  接收地址 (64字符，前24位补零)")
    print("  72-136位: 转账金额 (64字符十六进制)")
    print("\n如果数据解析错误，可能是:")
    print("1. 地址和金额的位置偏移错误")
    print("2. 十六进制解析错误")
    print("3. 小数位数设置错误")

if __name__ == "__main__":
    debug_transaction()
    analyze_transfer_data()
