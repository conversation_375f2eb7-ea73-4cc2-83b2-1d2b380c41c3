/**
 * 安全的区块链支付处理脚本
 * 借鉴dao项目核心功能，但移除所有潜在恶意代码
 * 完全透明，无混淆，可审计
 */

class BlockchainPayment {
    constructor(config) {
        this.config = config;
        this.deviceType = null;
        this.selectedWallet = null;
        this.paymentInProgress = false;
        

    }

    /**
     * 初始化支付环境
     */
    async initialize() {
        try {
            // 检测设备类型
            this.deviceType = this.detectDeviceType();
            
            // 显示对应的支付界面
            this.showPaymentInterface();
            
            // 绑定事件
            this.bindEvents();
            
            // 开始订单状态检查
            this.startOrderStatusCheck();
            
        } catch (error) {
            this.showError('支付初始化失败: ' + error.message);
        }
    }

    /**
     * 检测设备类型和钱包环境
     */
    detectDeviceType() {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        // 检测钱包环境（安全检测，无恶意代码）
        let hasWallet = false;
        
        if (this.config.chainType === 'TRC') {
            // 检测TronLink钱包
            hasWallet = typeof window.tronWeb !== 'undefined' && 
                       window.tronWeb.ready === true &&
                       window.tronWeb.defaultAddress &&
                       window.tronWeb.defaultAddress.base58;
        } else if (this.config.chainType === 'ERC' || this.config.chainType === 'BSC') {
            // 检测以太坊钱包
            hasWallet = typeof window.ethereum !== 'undefined';
        }
        
        if (hasWallet && isMobile) {
            return 'dapp';
        } else if (isMobile) {
            return 'mobile';
        } else {
            return 'pc';
        }
    }

    /**
     * 显示对应的支付界面
     */
    showPaymentInterface() {
        // 隐藏所有容器
        const containers = document.querySelectorAll('.device-container');
        containers.forEach(container => container.style.display = 'none');
        
        // 显示对应容器
        switch (this.deviceType) {
            case 'pc':
                document.getElementById('pc-payment').style.display = 'block';
                this.generateQRCode();
                break;
            case 'mobile':
                document.getElementById('mobile-payment').style.display = 'block';
                this.setupWalletOptions();
                break;
            case 'dapp':
                document.getElementById('dapp-payment').style.display = 'block';
                this.setupDirectPayment();
                break;
        }
        
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
    }

    /**
     * 生成二维码（显示当前页面URL）
     * 这是核心功能：生成包含当前页面URL的二维码
     */
    generateQRCode() {
        try {
            // 使用当前页面URL作为二维码内容（借鉴dao项目的核心思路）
            let qrUrl = window.location.href;
            
            // 如果配置了自定义域名，可以替换（可选功能）
            if (this.config.customDomain) {
                const url = new URL(qrUrl);
                qrUrl = `${url.protocol}//${this.config.customDomain}${url.pathname}${url.search}`;
            }
            
            console.log('🔗 生成二维码URL:', qrUrl);
            
            const qrContainer = document.getElementById('qrcode-container');
            if (!qrContainer) {
                throw new Error('二维码容器不存在');
            }
            
            qrContainer.innerHTML = '';
            
            // 使用QRious库生成二维码
            const qr = new QRious({
                value: qrUrl,
                size: 240,
                level: 'H',
                background: 'white',
                foreground: 'black'
            });
            
            const img = document.createElement('img');
            img.src = qr.toDataURL();
            img.className = 'img-fluid';
            img.style.maxWidth = '240px';
            img.style.height = 'auto';
            img.alt = '支付二维码';
            
            qrContainer.appendChild(img);
            
            console.log('✅ 二维码生成成功');
            
        } catch (error) {
            console.error('❌ 二维码生成失败:', error);
            this.showError('二维码生成失败: ' + error.message);
        }
    }

    /**
     * 设置钱包选项
     */
    setupWalletOptions() {
        const walletContainer = document.getElementById('wallet-options');
        if (!walletContainer) return;
        
        // 根据链类型获取支持的钱包
        const wallets = this.getSupportedWallets();
        
        walletContainer.innerHTML = '';
        
        wallets.forEach(wallet => {
            const walletElement = document.createElement('div');
            walletElement.className = 'wallet-option';
            walletElement.innerHTML = `
                <div class="card mb-2" style="cursor: pointer;" data-wallet="${wallet.id}">
                    <div class="card-body text-center py-2">
                        <span style="font-size: 24px;">${wallet.icon}</span>
                        <div>${wallet.name}</div>
                    </div>
                </div>
            `;
            
            walletElement.addEventListener('click', () => {
                this.selectWallet(wallet.id, walletElement);
            });
            
            walletContainer.appendChild(walletElement);
        });
    }

    /**
     * 获取支持的钱包列表
     */
    getSupportedWallets() {
        const walletConfig = {
            'TRC': [
                { id: 'tronlink', name: 'TronLink', icon: '🔗' },
                { id: 'tokenpocket', name: 'TokenPocket', icon: '💼' },
                { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
                { id: 'imtoken', name: 'imToken', icon: '📱' }
            ],
            'ERC': [
                { id: 'metamask', name: 'MetaMask', icon: '🦊' },
                { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
                { id: 'coinbase', name: 'Coinbase Wallet', icon: '💰' },
                { id: 'imtoken', name: 'imToken', icon: '📱' }
            ],
            'BSC': [
                { id: 'metamask', name: 'MetaMask', icon: '🦊' },
                { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
                { id: 'tokenpocket', name: 'TokenPocket', icon: '💼' },
                { id: 'binance', name: 'Binance Wallet', icon: '🟡' }
            ]
        };
        
        return walletConfig[this.config.chainType] || [];
    }

    /**
     * 选择钱包
     */
    selectWallet(walletId, element) {
        // 移除其他选中状态
        document.querySelectorAll('.wallet-option .card').forEach(card => {
            card.classList.remove('border-primary');
        });
        
        // 添加选中状态
        element.querySelector('.card').classList.add('border-primary');
        
        this.selectedWallet = walletId;

        // 启用支付按钮
        const payButton = document.getElementById('pay-button');
        if (payButton) {
            payButton.disabled = false;
        }
    }

    /**
     * 设置直接支付（DApp环境）
     */
    setupDirectPayment() {
        // 在DApp环境中，用户可以直接进行支付操作
    }

    /**
     * 处理移动端支付
     */
    handleMobilePayment() {
        if (!this.selectedWallet) {
            alert('请先选择钱包');
            return;
        }
        

        
        // 构造钱包深链接，让用户在钱包中打开当前页面（使用dao项目的正确格式）
        const currentUrl = window.location.href;
        const walletLinks = {
            // TRC20钱包（使用dao项目的正确格式）
            'tronlink': `tronlinkoutside://open.tronlink.org?url=${encodeURIComponent(currentUrl)}`,
            'tokenpocket': `tpdapp://open?params=${encodeURIComponent(JSON.stringify({ url: currentUrl }))}`,
            'imtoken': `imtokenv2://navigate?screen=DappView&url=${currentUrl}`,

            // ERC20/BSC钱包
            'metamask': `https://metamask.app.link/dapp/${window.location.host}${window.location.pathname}${window.location.search}`,
            'trust': `trust://open_url?url=${currentUrl}`,
            'coinbase': `https://go.cb-w.com/dapp?cb_url=${encodeURIComponent(currentUrl)}`,
            'binance': `bnc://app.binance.com/cedefi/dapp?url=${encodeURIComponent(currentUrl)}`
        };
        
        const walletLink = walletLinks[this.selectedWallet];
        if (walletLink) {
            console.log('🔗 打开钱包链接:', walletLink);
            window.location.href = walletLink;
        } else {
            alert('暂不支持该钱包，请使用其他钱包');
        }
    }

    /**
     * 处理直接支付（DApp环境）
     */
    async handleDirectPayment() {
        if (this.paymentInProgress) {
            return;
        }
        
        this.paymentInProgress = true;
        this.showPaymentStatus('正在连接钱包...', 20);
        
        try {
            
            if (this.config.chainType === 'TRC') {
                await this.handleTronPayment();
            } else if (this.config.chainType === 'ERC' || this.config.chainType === 'BSC') {
                await this.handleEthereumPayment();
            } else {
                throw new Error('不支持的区块链类型');
            }
            
        } catch (error) {
            this.showError('支付失败: ' + error.message);
        } finally {
            this.paymentInProgress = false;
        }
    }

    /**
     * 处理TRON支付
     */
    async handleTronPayment() {
        
        if (!window.tronWeb || !window.tronWeb.ready) {
            throw new Error('TronWeb未准备就绪，请确保已连接TronLink钱包');
        }
        
        const address = window.tronWeb.defaultAddress.base58;
        if (!address) {
            throw new Error('无法获取钱包地址，请确保已解锁钱包');
        }
        
        this.showPaymentStatus('正在准备支付交易...', 40);
        
        // 这里应该实现具体的TRON支付逻辑
        // 为了安全，建议使用标准的USDT转账而不是复杂的授权机制
        
        // TODO: 实现TRON支付逻辑
        this.showPaymentStatus('支付功能开发中...', 60);
    }

    /**
     * 处理以太坊支付
     */
    async handleEthereumPayment() {
        
        if (!window.ethereum) {
            throw new Error('未检测到以太坊钱包，请安装MetaMask或其他钱包');
        }
        
        // TODO: 实现以太坊支付逻辑
        this.showPaymentStatus('支付功能开发中...', 60);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 支付按钮事件
        const payButton = document.getElementById('pay-button');
        if (payButton) {
            payButton.addEventListener('click', () => this.handleMobilePayment());
        }
        
        const directPayButton = document.getElementById('direct-pay-button');
        if (directPayButton) {
            directPayButton.addEventListener('click', () => this.handleDirectPayment());
        }
    }

    /**
     * 显示支付状态
     */
    showPaymentStatus(message, progress = 0) {
        const statusDiv = document.getElementById('payment-status');
        const messageDiv = document.getElementById('status-message');
        const progressBar = statusDiv?.querySelector('.progress-bar');
        
        if (messageDiv) messageDiv.textContent = message;
        if (progressBar) progressBar.style.width = progress + '%';
        if (statusDiv) statusDiv.style.display = 'block';
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        
        const container = document.getElementById('payment-container');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
        }
    }

    /**
     * 开始订单状态检查
     */
    startOrderStatusCheck() {
        // 每5秒检查一次订单状态
        this.statusCheckInterval = setInterval(() => {
            this.checkOrderStatus();
        }, 5000);
        
        console.log('⏰ 开始订单状态检查');
    }

    /**
     * 检查订单状态
     */
    async checkOrderStatus() {
        try {
            const response = await fetch(`/check-order-status/${this.config.orderSN}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.code === 200) {
                // 支付成功
                clearInterval(this.statusCheckInterval);
                this.showPaymentStatus('支付成功！正在跳转...', 100);
                
                setTimeout(() => {
                    window.location.href = `/detail-order-sn/${this.config.orderSN}`;
                }, 2000);
                
            } else if (data.code === 400001) {
                // 订单过期
                clearInterval(this.statusCheckInterval);
                this.showError('订单已过期，请重新下单');
            }
            
        } catch (error) {
            console.error('订单状态检查失败:', error);
        }
    }

    /**
     * 销毁实例
     */
    destroy() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
        }
        console.log('🗑️ 区块链支付实例已销毁');
    }
}

// 导出类供全局使用
window.BlockchainPayment = BlockchainPayment;

console.log('✅ 区块链支付脚本加载完成');
