#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速区块扫描工具 - 简化版本
专门用于快速查询特定地址的交易，减少日志噪音
"""

import json
import requests
import time
import random
from datetime import datetime

class QuickScanner:
    def __init__(self):
        self.load_config()
        
    def load_config(self):
        """加载配置"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.api_keys = config.get('api_keys', [])
            self.target_address = config.get('target_address', 'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ')
            scan_range = config.get('scan_range', {})
            self.start_block = scan_range.get('start_block', 74629570)
            self.end_block = scan_range.get('end_block', 74629580)
            
            # 代币小数位数映射
            self.token_decimals = {
                'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t': 6,   # USDT
                'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8': 6,   # USDC
                '41a614f803b6fd780986a42c78ec9c7f77e6ded13c': 6,
                '4199469fd5aa08cfe836395ce993c0ebd567e2c14f': 6,
                '4139dd12a54e2bab7c82aa14a1e158b34263d2d510': 6,
                '41c60a6f5c81431c97ed01b61698b6853557f3afd4': 6,
            }
            
            # 代理设置
            proxy_config = config.get('proxy', {})
            if proxy_config.get('enabled', True):
                proxy_url = proxy_config.get('url', 'http://127.0.0.1:7891')
                self.proxies = {"http": proxy_url, "https": proxy_url}
            else:
                self.proxies = {"http": None, "https": None}
                
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            exit(1)
    
    def get_random_api_key(self):
        """随机选择API Key"""
        return random.choice(self.api_keys) if self.api_keys else None
    
    def get_block_transactions(self, block_number, api_key):
        """获取区块交易"""
        try:
            url = "https://api.trongrid.io/wallet/getblockbynum"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"num": block_number}
            
            response = requests.post(url, json=data, headers=headers, 
                                   proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                return result.get('transactions', [])
            return []
        except:
            return []
    
    def hex_to_base58(self, hex_str):
        """十六进制转Base58"""
        try:
            import hashlib
            alphabet = "**********************************************************"
            hex_bytes = bytes.fromhex(hex_str)
            hash1 = hashlib.sha256(hex_bytes).digest()
            hash2 = hashlib.sha256(hash1).digest()
            checksum = hash2[:4]
            full_bytes = hex_bytes + checksum
            num = int.from_bytes(full_bytes, 'big')
            encoded = ""
            while num > 0:
                num, remainder = divmod(num, 58)
                encoded = alphabet[remainder] + encoded
            for byte in full_bytes:
                if byte == 0:
                    encoded = alphabet[0] + encoded
                else:
                    break
            return encoded
        except:
            return ""
    
    def hex_to_tron_address(self, hex_address):
        """十六进制地址转TRON地址"""
        try:
            clean_hex = hex_address.strip().lower()
            if clean_hex.startswith('0x'):
                clean_hex = clean_hex[2:]
            
            if len(clean_hex) == 64:
                clean_hex = clean_hex[24:]
            elif len(clean_hex) != 40:
                return ""
            
            tron_hex = "41" + clean_hex
            return self.hex_to_base58(tron_hex)
        except:
            return ""
    
    def get_token_decimals(self, contract_address):
        """获取代币小数位数"""
        return self.token_decimals.get(contract_address, 6)  # 默认6位小数
    
    def analyze_transaction(self, tx):
        """分析交易"""
        try:
            tx_hash = tx.get('txID', '')
            found_transactions = []
            
            if 'raw_data' not in tx or 'contract' not in tx['raw_data']:
                return None
            
            for contract in tx['raw_data']['contract']:
                # TRC20转账
                if contract.get('type') == 'TriggerSmartContract':
                    parameter = contract.get('parameter', {})
                    value = parameter.get('value', {})
                    contract_address = value.get('contract_address', '')
                    data = value.get('data', '')
                    
                    if data and len(data) >= 136:
                        method_signature = data[0:8]   # 方法签名
                        to_address_hex = data[8:72]    # 接收地址 (64字符，前24位补零)
                        amount_hex = data[72:136]      # 转账金额
                        
                        try:
                            to_address = self.hex_to_tron_address(to_address_hex)
                            from_address = value.get('owner_address', '')
                            
                            if to_address == self.target_address or from_address == self.target_address:
                                decimals = self.get_token_decimals(contract_address)
                                raw_amount = int(amount_hex, 16)
                                amount = raw_amount / (10 ** decimals)
                                
                                # 确定代币类型
                                if contract_address == 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t':
                                    token_type = 'USDT'
                                elif contract_address == 'TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8':
                                    token_type = 'USDC'
                                else:
                                    token_type = 'TRC20'
                                
                                direction = 'IN' if to_address == self.target_address else 'OUT'
                                
                                found_transactions.append({
                                    'tx_hash': tx_hash,
                                    'type': f'{token_type}_TRANSFER',
                                    'from_address': from_address,
                                    'to_address': to_address,
                                    'amount': amount,
                                    'raw_amount': raw_amount,
                                    'decimals': decimals,
                                    'contract': contract_address,
                                    'direction': direction
                                })
                        except:
                            continue
                
                # TRX转账
                elif contract.get('type') == 'TransferContract':
                    parameter = contract.get('parameter', {})
                    value = parameter.get('value', {})
                    from_address = value.get('owner_address', '')
                    to_address = value.get('to_address', '')
                    amount = value.get('amount', 0) / 1000000
                    
                    if to_address == self.target_address or from_address == self.target_address:
                        direction = 'IN' if to_address == self.target_address else 'OUT'
                        found_transactions.append({
                            'tx_hash': tx_hash,
                            'type': 'TRX_TRANSFER',
                            'from_address': from_address,
                            'to_address': to_address,
                            'amount': amount,
                            'direction': direction
                        })
            
            return found_transactions if found_transactions else None
        except:
            return None
    
    def scan(self):
        """执行扫描"""
        print(f"🔍 快速扫描区块 {self.start_block} - {self.end_block}")
        print(f"🎯 目标地址: {self.target_address}")
        print("=" * 60)
        
        if not self.api_keys:
            print("❌ 请配置API Keys")
            return
        
        found_transactions = []
        total_tx_count = 0
        
        for block_num in range(self.start_block, self.end_block + 1):
            api_key = self.get_random_api_key()
            transactions = self.get_block_transactions(block_num, api_key)
            total_tx_count += len(transactions)
            
            print(f"📦 区块 {block_num}: {len(transactions)} 个交易", end="")
            
            block_found = 0
            for tx in transactions:
                results = self.analyze_transaction(tx)
                if results:
                    for result in results:
                        result['block_number'] = block_num
                        found_transactions.append(result)
                        block_found += 1
            
            if block_found > 0:
                print(f" -> 🎯 发现 {block_found} 个相关交易")
            else:
                print()
            
            time.sleep(0.1)  # 简短延迟
        
        print("=" * 60)
        print(f"✅ 扫描完成:")
        print(f"   - 扫描区块: {self.end_block - self.start_block + 1} 个")
        print(f"   - 总交易数: {total_tx_count}")
        print(f"   - 相关交易: {len(found_transactions)} 个")
        
        if found_transactions:
            print("\n📋 发现的交易:")
            for i, tx in enumerate(found_transactions, 1):
                direction_emoji = "📥" if tx['direction'] == 'IN' else "📤"
                print(f"\n{i}. {direction_emoji} {tx['type']}")
                print(f"   交易哈希: {tx['tx_hash']}")
                print(f"   区块号: {tx['block_number']}")
                print(f"   金额: {tx['amount']}")
                if 'raw_amount' in tx:
                    print(f"   原始金额: {tx['raw_amount']} (小数位: {tx['decimals']})")
                print(f"   发送方: {tx.get('from_address', 'N/A')}")
                print(f"   接收方: {tx.get('to_address', 'N/A')}")
                if 'contract' in tx:
                    print(f"   合约: {tx['contract']}")
            
            # 保存结果
            filename = f"quick_scan_{self.start_block}_{self.end_block}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(found_transactions, f, ensure_ascii=False, indent=2)
            print(f"\n💾 结果已保存到: {filename}")
        else:
            print("\n📭 未发现相关交易")

if __name__ == "__main__":
    scanner = QuickScanner()
    scanner.scan()
