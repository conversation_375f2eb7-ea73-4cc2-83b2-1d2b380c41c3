<div class="background"></div>
<div class="header" style="">
    <div class="layui-row">
        <div class="layui-col-md8 layui-col-md-offset2 layui-col-sm12">
            <div class="header-box">
                <!-- Logo区域 -->
                <div class="logo-section">
                    <a href="/" class="logo-link @if(\Illuminate\Support\Facades\Request::path() == '/') active @endif">
                        <img src="{{ picture_ulr(dujiaoka_config_get('img_logo')) }}" alt="">
                        <div class="info">{{ dujiaoka_config_get('text_logo') }}</div>
                    </a>
                </div>

                <!-- 导航菜单区域 -->
                <div class="nav-menu layui-hide-xs">
                    <a href="/" class="nav-item @if(\Illuminate\Support\Facades\Request::path() == '/') active @endif">
                        <svg t="1603120404646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1611" width="18" height="18">
                            <path d="M512 85.333333l384 341.333334v512H640V640H384v298.666666H128V426.666667z" fill="currentColor"></path>
                        </svg>
                        <span>{{ __('dujiaoka.home_page') }}</span>
                    </a>
                    <a href="{{ url('order-search') }}" class="nav-item @if(\Illuminate\Support\Facades\Request::url() == url('order-search')) active @endif">
                        <svg t="1602923269232" class="icon" viewBox="0 0 1024 1024" version="1.1"
                             xmlns="http://www.w3.org/2000/svg" p-id="4816" width="18" height="18">
                            <path d="M965.6 447.2 752 447.2c-14.4 0-26.4-12-26.4-26.4 0-14.4 12-26.4 26.4-26.4l213.6 0c14.4 0 26.4 12 26.4 26.4C992.8 435.2 980.8 447.2 965.6 447.2zM965.6 233.6 699.2 233.6c-14.4 0-26.4-12-26.4-26.4 0-14.4 12-26.4 26.4-26.4l267.2 0c14.4 0 26.4 12 26.4 26.4C992.8 221.6 980.8 233.6 965.6 233.6zM606.4 623.2l156.8 156.8c21.6 21.6 21.6 56.8 0 78.4-21.6 21.6-56.8 21.6-78.4 0L528 701.6c-16-16-20-39.2-12-59.2-51.2 44-117.6 72-190.4 72-162.4 0-293.6-131.2-293.6-293.6s131.2-293.6 293.6-293.6 293.6 131.2 293.6 293.6c0 72.8-28 139.2-72 190.4C567.2 603.2 590.4 607.2 606.4 623.2zM324.8 233.6c-103.2 0-187.2 84-187.2 187.2s84 187.2 187.2 187.2S512 523.2 512 420 428 233.6 324.8 233.6zM805.6 607.2l160 0c14.4 0 26.4 12 26.4 26.4 0 14.4-12 26.4-26.4 26.4l-160 0c-14.4 0-26.4-12-26.4-26.4C779.2 619.2 791.2 607.2 805.6 607.2z"
                                  p-id="4817" fill="currentColor"></path>
                        </svg>
                        <span>{{ __('dujiaoka.order_search') }}</span>
                    </a>

                    @if(\Illuminate\Support\Facades\Request::path() == '/')
                        <!-- 首页搜索框 -->
                        <div class="search-box">
                            <input type="text" id="searchText" placeholder="{{ __('dujiaoka.search_goods_name') }}" class="search-input">
                            <button type="button" id="searchBtn" class="search-btn">
                                <svg class="icon" viewBox="0 0 1024 1024" width="16" height="16">
                                    <path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" fill="currentColor"></path>
                                </svg>
                            </button>
                        </div>
                    @endif
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="mobile-menu-toggle layui-show-xs-inline-block">
                    <button class="menu-btn" onclick="toggleMobileMenu()">
                        <svg class="icon" viewBox="0 0 1024 1024" width="20" height="20">
                            <path d="M128 256h768v85.333333H128V256z m0 213.333333h768v85.333334H128v-85.333334z m0 213.333334h768V768H128v-85.333333z" fill="currentColor"></path>
                        </svg>
                    </button>
                </div>

                @yield('notice')
            </div>
        </div>
    </div>

    <!-- 移动端下拉菜单 -->
    <div class="mobile-menu layui-show-xs-block" id="mobileMenu" style="display: none;">
        <div class="layui-row">
            <div class="layui-col-md8 layui-col-md-offset2 layui-col-sm12">
                <div class="mobile-nav-items">
                    <a href="/" class="mobile-nav-item @if(\Illuminate\Support\Facades\Request::path() == '/') active @endif">
                        <svg t="1603120404646" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1611" width="18" height="18">
                            <path d="M512 85.333333l384 341.333334v512H640V640H384v298.666666H128V426.666667z" fill="currentColor"></path>
                        </svg>
                        <span>{{ __('dujiaoka.home_page') }}</span>
                    </a>
                    <a href="{{ url('order-search') }}" class="mobile-nav-item @if(\Illuminate\Support\Facades\Request::url() == url('order-search')) active @endif">
                        <svg t="1602923269232" class="icon" viewBox="0 0 1024 1024" version="1.1"
                             xmlns="http://www.w3.org/2000/svg" p-id="4816" width="18" height="18">
                            <path d="M965.6 447.2 752 447.2c-14.4 0-26.4-12-26.4-26.4 0-14.4 12-26.4 26.4-26.4l213.6 0c14.4 0 26.4 12 26.4 26.4C992.8 435.2 980.8 447.2 965.6 447.2zM965.6 233.6 699.2 233.6c-14.4 0-26.4-12-26.4-26.4 0-14.4 12-26.4 26.4-26.4l267.2 0c14.4 0 26.4 12 26.4 26.4C992.8 221.6 980.8 233.6 965.6 233.6zM606.4 623.2l156.8 156.8c21.6 21.6 21.6 56.8 0 78.4-21.6 21.6-56.8 21.6-78.4 0L528 701.6c-16-16-20-39.2-12-59.2-51.2 44-117.6 72-190.4 72-162.4 0-293.6-131.2-293.6-293.6s131.2-293.6 293.6-293.6 293.6 131.2 293.6 293.6c0 72.8-28 139.2-72 190.4C567.2 603.2 590.4 607.2 606.4 623.2zM324.8 233.6c-103.2 0-187.2 84-187.2 187.2s84 187.2 187.2 187.2S512 523.2 512 420 428 233.6 324.8 233.6zM805.6 607.2l160 0c14.4 0 26.4 12 26.4 26.4 0 14.4-12 26.4-26.4 26.4l-160 0c-14.4 0-26.4-12-26.4-26.4C779.2 619.2 791.2 607.2 805.6 607.2z"
                                  p-id="4817" fill="currentColor"></path>
                        </svg>
                        <span>{{ __('dujiaoka.order_search') }}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@yield('notice_min')