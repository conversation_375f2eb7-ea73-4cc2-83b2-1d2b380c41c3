@extends('luna.layouts.default')

@section('content')
    <body>
    @include('luna.layouts._nav')
    <style>
        /* 确保导航栏保持原始样式 */
        .header .header-box {
            display: block !important;
        }

        .header .header-box a {
            float: left !important;
            display: inline-block !important;
        }

        .header .header-box .query {
            float: right !important;
            display: inline-block !important;
        }

        .header .header-box::after {
            content: "";
            display: table;
            clear: both;
        }

        /* 确保页面整体居中，不影响导航栏 */
        .main {
            min-height: calc(100vh - 120px); /* 减去导航栏高度 */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
            box-sizing: border-box;
            /* 确保在Luna背景上也能正常显示 */
            background: transparent;
        }

        /* 只对订单确认页面的layui-row进行居中，不影响导航栏 */
        .main .layui-row {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
        }

        .main .layui-col-md8 {
            display: flex;
            justify-content: center;
            width: 100%;
        }

        .main-box {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(60, 140, 231, 0.15);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .main-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #3C8CE7 10%, #00EAFF 100%);
        }

        .pay-title {
            text-align: center;
            font-size: 22px;
            font-weight: 700;
            color: #3C8CE7;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f8ff;
            position: relative;
        }

        .pay-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(135deg, #3C8CE7 10%, #00EAFF 100%);
        }

        .order-warning {
            background: linear-gradient(135deg, rgba(60, 140, 231, 0.1) 0%, rgba(0, 234, 255, 0.1) 100%);
            border: 1px solid rgba(60, 140, 231, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .order-warning::before {
            content: '⏰';
            font-size: 20px;
            margin-right: 8px;
        }

        .layui-table {
            margin: 25px auto;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .layui-table td {
            padding: 16px 20px;
            border-bottom: 1px solid #f5f5f5;
            transition: background-color 0.3s ease;
        }

        .layui-table tr:hover td {
            background-color: #f8fbff;
        }

        .layui-table tr:last-child td {
            border-bottom: none;
        }

        .layui-table td:first-child {
            text-align: right;
            color: #666;
            font-weight: 600;
            width: 40%;
            background: #fafbfc;
            border-right: 1px solid #f0f0f0;
        }

        .layui-table td:last-child {
            text-align: left;
            color: #333;
            font-weight: 500;
        }

        .highlight-value {
            color: #3C8CE7;
            font-weight: 700;
            font-size: 16px;
        }

        .pay-button-container {
            text-align: center;
            margin: 30px 0 20px 0;
        }

        .btn {
            display: inline-block;
            padding: 15px 40px;
            font-size: 18px;
            font-weight: 700;
            color: #fff;
            text-decoration: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #3C8CE7 10%, #00EAFF 100%);
            box-shadow: 0 8px 20px rgba(60, 140, 231, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(60, 140, 231, 0.4);
            color: #fff;
            text-decoration: none;
        }

        .btn:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .main {
                min-height: calc(100vh - 205px); /* 移动端导航栏高度205px */
                align-items: center; /* 保持垂直居中 */
                padding: 15px;
                margin-top: -145px; /* 配合Luna移动端导航栏的margin-bottom: -145px */
            }

            .main-box {
                margin: 0;
                padding: 20px;
                width: 100%;
            }

            .layui-table td:first-child {
                width: 35%;
                font-size: 14px;
            }

            .layui-table td {
                padding: 12px 15px;
            }

            .btn {
                padding: 12px 30px;
                font-size: 16px;
            }

            .main .layui-row {
                width: 100%;
                margin: 0;
            }

            .main .layui-col-md8 {
                width: 100% !important;
                margin: 0 !important;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1200px) {
            .main {
                min-height: calc(100vh - 140px);
            }
        }

        /* 小屏幕优化 */
        @media (max-width: 480px) {
            .main {
                min-height: calc(100vh - 205px);
                padding: 10px;
                margin-top: -145px; /* 保持与Luna移动端导航栏的一致性 */
            }

            .main-box {
                padding: 15px;
            }
        }
    </style>
    <div class="main">
        <div class="layui-row">
            <div class="layui-col-md8 layui-col-md-offset2 layui-col-sm12" style="display: flex; justify-content: center;">
                <div class="main-box" style="width: 100%; max-width: 600px;">
                    <div class="pay-title">
                        <svg style="margin-bottom: -6px;" t="1603120404646" class="icon" viewBox="0 0 1024 1024"
                             version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1611" width="27" height="27">
                            <path d="M320.512 428.032h382.976v61.44H320.512zM320.512 616.448h320.512v61.44H320.512z"
                                  fill="#00EAFF" p-id="1612" data-spm-anchor-id="a313x.7781069.0.i3"
                                  class="selected"></path>
                            <path
                                d="M802.816 937.984H221.184l-40.96-40.96V126.976l40.96-40.96h346.112l26.624 10.24 137.216 117.76 98.304 79.872 15.36 31.744v571.392l-41.984 40.96z m-540.672-81.92h500.736V345.088L677.888 276.48 550.912 167.936H262.144v688.128z"
                                fill="#3C8CE7" p-id="1613" data-spm-anchor-id="a313x.7781069.0.i0" class=""></path>
                        </svg>
                        {{ __('dujiaoka.confirm_order') }}
                    </div>

                    <div class="order-warning">
                        <strong>{{ __('dujiaoka.warning_title') }}</strong>{{ __('dujiaoka.date_to_expired_order', ['min' => dujiaoka_config_get('order_expire_time', 5)]) }}
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                            <tr>
                                <td>{{ __('order.fields.order_sn') }}：</td>
                                <td><span class="highlight-value">{{ $order_sn }}</span></td>
                            </tr>
                            <tr>
                                <td>{{ __('order.fields.title') }}：</td>
                                <td>{{ $title }}</td>
                            </tr>
                            <tr>
                                <td>{{ __('order.fields.goods_price') }}：</td>
                                <td>{{ __('dujiaoka.money_symbol') }}{{ $goods_price }}</td>
                            </tr>
                            <tr>
                                <td>{{ __('order.fields.buy_amount') }}：</td>
                                <td><span style="color: #00EAFF; font-weight: 600;">x {{ $buy_amount }}</span></td>
                            </tr>
                            @if(isset($coupon))
                                <tr>
                                    <td>{{ __('order.fields.coupon_id') }}：</td>
                                    <td><span style="color: #52c41a; font-weight: 600;">{{ $coupon['coupon'] }}</span></td>
                                </tr>
                                <tr>
                                    <td>{{ __('order.fields.coupon_discount_price') }}：</td>
                                    <td><span style="color: #52c41a; font-weight: 600;">-{{ __('dujiaoka.money_symbol') }}{{ $coupon_discount_price }}</span></td>
                                </tr>
                            @endif
                            @if($wholesale_discount_price > 0 )
                                <tr>
                                    <td>{{ __('order.fields.wholesale_discount_price') }}：</td>
                                    <td><span style="color: #52c41a; font-weight: 600;">-{{ __('dujiaoka.money_symbol') }}{{ $wholesale_discount_price }}</span></td>
                                </tr>
                            @endif
                            <tr style="background: linear-gradient(135deg, rgba(60, 140, 231, 0.05) 0%, rgba(0, 234, 255, 0.05) 100%);">
                                <td style="color: #3C8CE7; font-weight: 700;">{{ __('order.fields.actual_price') }}：</td>
                                <td><span class="highlight-value" style="font-size: 20px;">{{ __('dujiaoka.money_symbol') }}{{ $actual_price }}</span></td>
                            </tr>
                            <tr>
                                <td>{{ __('dujiaoka.email') }}：</td>
                                <td>{{ $email }}</td>
                            </tr>
                            @if($info)
                                @php
                                    preg_match_all('/(\[.*?\])/m', $info, $matches, PREG_SET_ORDER, 0);

                                    foreach ($matches as $item) {
                                        $str = $item[1] ?? '';
                                        if($str){
                                            $info = str_replace($str,'',$info);
                                        }
                                    }
                                @endphp
                                <tr>
                                    <td>{{ __('dujiaoka.order_information') }}：</td>
                                    <td>{{ $info }}</td>
                                </tr>
                            @endif
                            <tr>
                                <td>{{ __('dujiaoka.payment_method') }}：</td>
                                <td><span style="color: #3C8CE7; font-weight: 600;">{{ str_replace(['æ"¯ä»˜', 'USDTæ"¯ä»˜'], ['支付', 'USDT支付'], $pay['pay_name']) }}</span></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="pay-button-container">
                            <a href="{{ url('pay-gateway', ['handle' => urlencode($pay['pay_handleroute']),'payway' => $pay['pay_check'], 'orderSN' => $order_sn]) }}" class="btn">
                                💳 {{ __('dujiaoka.pay_immediately') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('luna.layouts._footer')

    <div class="query-m">
        <a href="{{ url('order-search') }}">
            <svg t="1602926403006" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                 p-id="3391" width="30" height="30">
                <path d="M320.512 428.032h382.976v61.44H320.512zM320.512 616.448h320.512v61.44H320.512z" fill="#ffffff"
                      p-id="3392" data-spm-anchor-id="a313x.7781069.0.i38" class="selected"></path>
                <path
                    d="M802.816 937.984H221.184l-40.96-40.96V126.976l40.96-40.96h346.112l26.624 10.24 137.216 117.76 98.304 79.872 15.36 31.744v571.392l-41.984 40.96z m-540.672-81.92h500.736V345.088L677.888 276.48 550.912 167.936H262.144v688.128z"
                    fill="#ffffff" p-id="3393" data-spm-anchor-id="a313x.7781069.0.i37" class="selected"></path>
            </svg>
            <span>{{ __('luna.order_search_m') }}</span>
        </a>
    </div>
    </body>
@endsection


