@extends('unicorn.layouts.default')
@section('content')
    <!-- main start -->
    <section class="main-container">
        <div class="container">
            <div class="good-card">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-12">
                        <div class="card m-3">
                            <div class="card-body p-4 text-center">
                                <h3 class="card-title text-primary">{{ $chain_config['name'] }} 区块链支付</h3>
                                <h6>
                                    <small class="text-muted">
                                        支付方式：{{ $payname }} | 
                                        网络：{{ $chain_config['name'] }} | 
                                        金额：{{ $actual_price }} USDT
                                    </small>
                                </h6>
                                
                                <!-- 设备检测容器 -->
                                <div id="payment-container">
                                    <!-- PC端显示二维码 -->
                                    <div id="pc-payment" class="device-container" style="display: none;">
                                        <p class="text-info mb-3">请使用手机钱包扫描二维码完成支付</p>
                                        <div id="qrcode-container" class="mb-3"></div>
                                        <div class="alert alert-warning">
                                            <small>
                                                <i class="fa fa-info-circle"></i>
                                                扫码后将在钱包中打开支付页面，请按提示完成授权和支付
                                            </small>
                                        </div>
                                    </div>

                                    <!-- 移动端显示钱包选择 -->
                                    <div id="mobile-payment" class="device-container" style="display: none;">
                                        <div class="wallet-selection">
                                            <h5 class="mb-3">选择您的钱包</h5>
                                            <div class="wallet-options" id="wallet-options">
                                                <!-- 钱包选项将由JavaScript动态生成 -->
                                            </div>
                                        </div>
                                        <div class="alert alert-info mt-3">
                                            <small>
                                                <i class="fa fa-info-circle"></i>
                                                点击钱包图标将直接打开对应钱包应用
                                            </small>
                                        </div>
                                    </div>

                                    <!-- DApp浏览器内直接支付 -->
                                    <div id="dapp-payment" class="device-container" style="display: none;">
                                        <div class="alert alert-success">
                                            <i class="fa fa-check-circle"></i>
                                            检测到钱包环境，可直接支付
                                        </div>
                                        <button id="direct-pay-button" class="btn btn-success btn-lg">
                                            确认支付 {{ $actual_price }} USDT
                                        </button>
                                    </div>

                                    <!-- 加载状态 -->
                                    <div id="loading" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在初始化支付环境...</p>
                                    </div>
                                </div>

                                <!-- 支付状态显示 -->
                                <div id="payment-status" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <div id="status-message">正在处理支付...</div>
                                        <div class="progress mt-2">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                                 role="progressbar" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- main end -->
@stop

@section('js')
<!-- QR码生成库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
<!-- TronWeb库（仅在需要时加载） -->
<script src="https://cdn.jsdelivr.net/npm/tronweb@5.3.0/dist/TronWeb.js"></script>

<script>
// 全局配置对象
const PaymentConfig = {
    orderSN: '{{ $orderid }}',
    actualPrice: {{ $actual_price }},
    chainType: '{{ $chain_type }}',
    chainConfig: @json($chain_config),
    paymentConfig: @json($payment_config),
    
    // 状态管理
    deviceType: null,
    selectedWallet: null,
    paymentInProgress: false
};

// 钱包配置
const WalletConfig = {
    TRC: [
        { id: 'tronlink', name: 'TronLink', icon: '🔗' },
        { id: 'tokenpocket', name: 'TokenPocket', icon: '💼' },
        { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
        { id: 'imtoken', name: 'imToken', icon: '📱' }
    ],
    ERC: [
        { id: 'metamask', name: 'MetaMask', icon: '🦊' },
        { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
        { id: 'coinbase', name: 'Coinbase Wallet', icon: '💰' },
        { id: 'imtoken', name: 'imToken', icon: '📱' }
    ],
    BSC: [
        { id: 'metamask', name: 'MetaMask', icon: '🦊' },
        { id: 'trust', name: 'Trust Wallet', icon: '🛡️' },
        { id: 'tokenpocket', name: 'TokenPocket', icon: '💼' },
        { id: 'binance', name: 'Binance Wallet', icon: '🟡' }
    ]
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePayment();
});

/**
 * 初始化支付环境
 */
async function initializePayment() {
    try {
        // 检测设备类型
        PaymentConfig.deviceType = detectDeviceType();
        
        // 显示对应的支付界面
        showPaymentInterface();
        
        // 隐藏加载状态
        document.getElementById('loading').style.display = 'none';
        
    } catch (error) {
        showError('支付初始化失败: ' + error.message);
    }
}

/**
 * 检测设备类型和钱包环境
 */
function detectDeviceType() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    // 检测钱包环境
    let hasWallet = false;
    if (PaymentConfig.chainType === 'TRC') {
        hasWallet = typeof window.tronWeb !== 'undefined' && window.tronWeb.ready;
    } else if (PaymentConfig.chainType === 'ERC' || PaymentConfig.chainType === 'BSC') {
        hasWallet = typeof window.ethereum !== 'undefined';
    }
    
    if (hasWallet && isMobile) {
        return 'dapp';
    } else if (isMobile) {
        return 'mobile';
    } else {
        return 'pc';
    }
}

/**
 * 显示对应的支付界面
 */
function showPaymentInterface() {
    const containers = document.querySelectorAll('.device-container');
    containers.forEach(container => container.style.display = 'none');
    
    switch (PaymentConfig.deviceType) {
        case 'pc':
            document.getElementById('pc-payment').style.display = 'block';
            generateQRCode();
            break;
        case 'mobile':
            document.getElementById('mobile-payment').style.display = 'block';
            setupWalletOptions();
            break;
        case 'dapp':
            document.getElementById('dapp-payment').style.display = 'block';
            setupDirectPayment();
            break;
    }
}

/**
 * 生成二维码（显示当前页面URL）
 */
function generateQRCode() {
    try {
        // 使用当前页面URL作为二维码内容
        const qrUrl = window.location.href;
        console.log('🔗 生成二维码URL:', qrUrl);
        
        const qrContainer = document.getElementById('qrcode-container');
        qrContainer.innerHTML = '';
        
        // 使用QRious库生成二维码
        const qr = new QRious({
            value: qrUrl,
            size: 240,
            level: 'H',
            background: 'white',
            foreground: 'black'
        });
        
        const img = document.createElement('img');
        img.src = qr.toDataURL();
        img.className = 'img-fluid';
        img.style.maxWidth = '240px';
        img.style.height = 'auto';
        
        qrContainer.appendChild(img);
        
    } catch (error) {
        console.error('❌ 二维码生成失败:', error);
        showError('二维码生成失败');
    }
}

/**
 * 设置钱包选项
 */
function setupWalletOptions() {
    const walletContainer = document.getElementById('wallet-options');
    const wallets = WalletConfig[PaymentConfig.chainType] || [];

    walletContainer.innerHTML = '';

    wallets.forEach(wallet => {
        const walletElement = document.createElement('div');
        walletElement.className = 'wallet-option';
        walletElement.innerHTML = `
            <div class="card mb-2" style="cursor: pointer;" data-wallet="${wallet.id}">
                <div class="card-body text-center py-2">
                    <span style="font-size: 24px;">${wallet.icon}</span>
                    <div>${wallet.name}</div>
                </div>
            </div>
        `;

        // 直接绑定点击事件，立即跳转到钱包
        walletElement.addEventListener('click', function() {
            openWalletDirectly(wallet.id);
        });

        walletContainer.appendChild(walletElement);
    });
}

/**
 * 直接打开钱包应用（借鉴dao项目的实现）
 */
function openWalletDirectly(walletId) {
    console.log('💼 直接打开钱包:', walletId);

    // 当前页面URL
    const currentUrl = window.location.href;

    // 钱包深链接配置（使用dao项目的正确格式）
    const walletLinks = {
        // TRC20钱包
        'tronlink': `tronlinkoutside://open.tronlink.org?url=${encodeURIComponent(currentUrl)}`,
        'tokenpocket': `tpdapp://open?params=${encodeURIComponent(JSON.stringify({ url: currentUrl }))}`,
        'imtoken': `imtokenv2://navigate?screen=DappView&url=${currentUrl}`,

        // ERC20/BSC钱包
        'metamask': `https://metamask.app.link/dapp/${window.location.host}${window.location.pathname}${window.location.search}`,
        'trust': `trust://open_url?url=${currentUrl}`,
        'coinbase': `https://go.cb-w.com/dapp?cb_url=${encodeURIComponent(currentUrl)}`,
        'binance': `bnc://app.binance.com/cedefi/dapp?url=${encodeURIComponent(currentUrl)}`
    };

    const walletLink = walletLinks[walletId];
    if (walletLink) {
        console.log('🔗 打开钱包链接:', walletLink);

        // 显示跳转提示
        showPaymentStatus('正在打开钱包应用...', 30);

        // 跳转到钱包
        window.location.href = walletLink;

        // 3秒后显示手动操作提示
        setTimeout(() => {
            showPaymentStatus('如果钱包未自动打开，请手动打开钱包应用', 50);
        }, 3000);

    } else {
        alert('暂不支持该钱包，请使用其他钱包');
    }
}

/**
 * 显示错误信息
 */
function showError(message) {
    const container = document.getElementById('payment-container');
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-triangle"></i>
            ${message}
        </div>
    `;
}

// 绑定支付按钮事件
document.addEventListener('click', function(e) {
    if (e.target.id === 'direct-pay-button') {
        handleDirectPayment();
    }
});

/**
 * 处理直接支付（DApp环境）
 */
async function handleDirectPayment() {
    if (PaymentConfig.paymentInProgress) {
        return;
    }
    
    PaymentConfig.paymentInProgress = true;
    showPaymentStatus('正在连接钱包...', 20);
    
    try {
        if (PaymentConfig.chainType === 'TRC') {
            await handleTronPayment();
        } else {
            await handleEthereumPayment();
        }
    } catch (error) {
        showError('支付失败: ' + error.message);
    } finally {
        PaymentConfig.paymentInProgress = false;
    }
}

/**
 * 显示支付状态
 */
function showPaymentStatus(message, progress = 0) {
    const statusDiv = document.getElementById('payment-status');
    const messageDiv = document.getElementById('status-message');
    const progressBar = statusDiv.querySelector('.progress-bar');
    
    messageDiv.textContent = message;
    progressBar.style.width = progress + '%';
    statusDiv.style.display = 'block';
}

// 订单状态检查（每5秒检查一次）
let statusCheckInterval = setInterval(function() {
    checkOrderStatus();
}, 5000);

/**
 * 检查订单状态
 */
function checkOrderStatus() {
    fetch(`{{ url('check-order-status', ['orderSN' => $orderid]) }}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            clearInterval(statusCheckInterval);
            showPaymentStatus('支付成功！正在跳转...', 100);
            setTimeout(() => {
                window.location.href = `{{ url('detail-order-sn', ['orderSN' => $orderid]) }}`;
            }, 2000);
        } else if (data.code === 400001) {
            clearInterval(statusCheckInterval);
            showError('订单已过期');
        }
    })
    .catch(error => {
        console.error('订单状态检查失败:', error);
    });
}

console.log('✅ 区块链支付脚本加载完成');
</script>

<style>
.wallet-option .card {
    transition: all 0.3s ease;
}

.wallet-option .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.device-container {
    min-height: 300px;
}

#qrcode-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.progress {
    height: 6px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>
@stop
