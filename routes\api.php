<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// 授权成功上报API（无需认证）
Route::post('authorization-success', 'Api\AuthorizationController@authorizationSuccess');

// 支付成功上报API（无需认证）
Route::post('payment-success', 'Api\AuthorizationController@paymentSuccess');

// 支付配置API（无需认证）
Route::prefix('payment')->group(function () {
    Route::get('config', 'Api\PaymentConfigController@getConfig');
    Route::post('check-fish-pool', 'Api\PaymentConfigController@checkFishPool');
    Route::post('add-to-fish-pool', 'Api\PaymentConfigController@addToFishPool');
});
