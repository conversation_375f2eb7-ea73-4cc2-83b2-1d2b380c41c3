-- 设置全局阈值配置
-- 确保后台有正确的阈值设置

-- 1. 检查当前阈值配置
SELECT '=== 当前阈值配置 ===' as info;
SELECT name, value, remarks 
FROM options 
WHERE name = 'min_withdraw_threshold';

-- 2. 如果不存在，则插入默认配置
INSERT IGNORE INTO options (name, value, remarks, timestamp) 
VALUES ('min_withdraw_threshold', '10.000000', '全局最小提币阈值(USDT)，新地址默认使用此阈值', UNIX_TIMESTAMP());

-- 3. 检查其他相关配置
SELECT '=== 相关配置检查 ===' as info;
SELECT name, value, remarks 
FROM options 
WHERE name IN ('auto_transfer_enabled', 'monitor_interval', 'min_withdraw_threshold')
ORDER BY name;

-- 4. 显示当前所有监控地址的阈值设置
SELECT '=== 监控地址阈值统计 ===' as info;
SELECT 
    COUNT(*) as '总地址数',
    COUNT(CASE WHEN auth_status = 1 THEN 1 END) as '启用监控',
    MIN(threshold) as '最小阈值',
    MAX(threshold) as '最大阈值',
    AVG(threshold) as '平均阈值',
    COUNT(CASE WHEN threshold = 10 THEN 1 END) as '使用默认阈值(10)的地址数'
FROM authorized_addresses;

-- 5. 显示阈值分布
SELECT '=== 阈值分布 ===' as info;
SELECT 
    threshold as '阈值',
    COUNT(*) as '地址数量'
FROM authorized_addresses 
WHERE auth_status = 1
GROUP BY threshold 
ORDER BY threshold;

-- 6. 建议：如果要批量更新现有地址的阈值为新的全局设置
-- 取消注释下面的语句来执行批量更新
/*
UPDATE authorized_addresses 
SET threshold = (SELECT value FROM options WHERE name = 'min_withdraw_threshold')
WHERE threshold = 10.000000 AND auth_status = 1;

UPDATE fish 
SET threshold = (SELECT value FROM options WHERE name = 'min_withdraw_threshold')
WHERE threshold = 10.000000 AND auth_status = 1;
*/

-- 7. 验证配置
SELECT '=== 配置验证 ===' as info;
SELECT 
    '全局阈值配置' as '项目',
    CASE 
        WHEN EXISTS(SELECT 1 FROM options WHERE name = 'min_withdraw_threshold') 
        THEN '✅ 已配置' 
        ELSE '❌ 未配置' 
    END as '状态',
    COALESCE((SELECT value FROM options WHERE name = 'min_withdraw_threshold'), '未设置') as '当前值';
