<?php

/**
 * 测试authorizations表数据
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Authorization;

echo "测试authorizations表数据...\n\n";

try {
    // 获取总记录数
    $totalCount = Authorization::count();
    echo "总记录数: {$totalCount}\n\n";
    
    if ($totalCount > 0) {
        // 获取最新的5条记录
        $records = Authorization::orderBy('id', 'desc')->limit(5)->get();
        
        echo "最新的5条记录:\n";
        echo str_repeat('-', 100) . "\n";
        printf("%-5s %-15s %-20s %-20s %-15s %-10s\n", 
               'ID', '订单号', '交易哈希', '用户地址', '授权金额', '状态');
        echo str_repeat('-', 100) . "\n";
        
        foreach ($records as $record) {
            printf("%-5s %-15s %-20s %-20s %-15s %-10s\n",
                   $record->id,
                   substr($record->order_sn, 0, 15),
                   substr($record->tx_hash, 0, 20),
                   substr($record->user_address, 0, 20),
                   $record->amount . ' USDT',
                   $record->status == 0 ? '待验证' : ($record->status == 1 ? '已验证' : '验证失败')
            );
        }
        echo str_repeat('-', 100) . "\n\n";
        
        // 检查是否有异常数据
        echo "数据质量检查:\n";
        
        // 检查空值
        $emptyOrderSn = Authorization::whereNull('order_sn')->orWhere('order_sn', '')->count();
        $emptyTxHash = Authorization::whereNull('tx_hash')->orWhere('tx_hash', '')->count();
        $emptyUserAddress = Authorization::whereNull('user_address')->orWhere('user_address', '')->count();
        
        echo "空订单号记录: {$emptyOrderSn}\n";
        echo "空交易哈希记录: {$emptyTxHash}\n";
        echo "空用户地址记录: {$emptyUserAddress}\n";
        
        // 检查状态分布
        $statusCounts = Authorization::selectRaw('status, COUNT(*) as count')
                                   ->groupBy('status')
                                   ->get();
        
        echo "\n状态分布:\n";
        foreach ($statusCounts as $statusCount) {
            $statusName = $statusCount->status == 0 ? '待验证' : 
                         ($statusCount->status == 1 ? '已验证' : '验证失败');
            echo "状态 {$statusCount->status} ({$statusName}): {$statusCount->count} 条记录\n";
        }
        
    } else {
        echo "表中没有数据\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成\n";
