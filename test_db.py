#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询测试脚本
"""

import pymysql

def test_database_query():
    # 数据库配置
    db_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'dujiaoka',
        'password': '19841020',
        'database': 'dujiaoka',
        'charset': 'utf8mb4'
    }
    
    test_address = "TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ"
    
    try:
        connection = pymysql.connect(**db_config)
        print(f"✅ 数据库连接成功")
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 1. 检查数据库基本信息
            cursor.execute("SELECT DATABASE() as db_name")
            db_info = cursor.fetchone()
            print(f"📋 当前数据库: {db_info['db_name']}")
            
            # 2. 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'authorized_addresses'")
            table_exists = cursor.fetchone()
            print(f"📋 authorized_addresses表存在: {table_exists is not None}")
            
            # 3. 查询总记录数
            cursor.execute("SELECT COUNT(*) as total FROM authorized_addresses")
            total = cursor.fetchone()
            print(f"📊 总记录数: {total['total']}")
            
            # 4. 查询所有记录
            cursor.execute("SELECT id, user_address, auth_status FROM authorized_addresses")
            all_records = cursor.fetchall()
            print(f"📋 所有记录:")
            for record in all_records:
                print(f"   ID: {record['id']}, 地址: {record['user_address']}, 状态: {record['auth_status']} (类型: {type(record['auth_status']).__name__})")
            
            # 5. 精确查询测试地址
            cursor.execute("SELECT * FROM authorized_addresses WHERE user_address = %s", (test_address,))
            exact_match = cursor.fetchone()
            print(f"🎯 精确匹配结果:")
            if exact_match:
                print(f"   找到记录: ID={exact_match['id']}, auth_status={exact_match['auth_status']}")
                print(f"   完整记录: {exact_match}")
            else:
                print(f"   未找到记录")
            
            # 6. 测试不同的auth_status查询条件
            print(f"🔍 测试不同查询条件:")
            
            # 条件1: auth_status = 1
            cursor.execute("SELECT COUNT(*) as count FROM authorized_addresses WHERE auth_status = 1")
            count1 = cursor.fetchone()
            print(f"   auth_status = 1: {count1['count']}条")
            
            # 条件2: auth_status = '1'
            cursor.execute("SELECT COUNT(*) as count FROM authorized_addresses WHERE auth_status = '1'")
            count2 = cursor.fetchone()
            print(f"   auth_status = '1': {count2['count']}条")
            
            # 条件3: 组合条件
            cursor.execute("SELECT COUNT(*) as count FROM authorized_addresses WHERE auth_status IN (1, '1', true, 'true')")
            count3 = cursor.fetchone()
            print(f"   auth_status IN (1, '1', true, 'true'): {count3['count']}条")
            
            # 7. 测试具体地址的查询
            print(f"🎯 测试具体地址查询:")
            
            # Python脚本的原始查询
            cursor.execute("""
                SELECT id, user_address, usdt_balance, threshold, total_collected, 
                       last_balance_check, auth_status 
                FROM authorized_addresses 
                WHERE user_address = %s AND auth_status IN (1, '1', true, 'true')
            """, (test_address,))
            result = cursor.fetchone()
            
            if result:
                print(f"   ✅ 查询成功: {result}")
            else:
                print(f"   ❌ 查询失败")
                
                # 尝试不带auth_status条件的查询
                cursor.execute("""
                    SELECT id, user_address, usdt_balance, threshold, total_collected, 
                           last_balance_check, auth_status 
                    FROM authorized_addresses 
                    WHERE user_address = %s
                """, (test_address,))
                result_no_status = cursor.fetchone()
                
                if result_no_status:
                    print(f"   不带状态条件查询成功: {result_no_status}")
                    print(f"   auth_status值: {result_no_status['auth_status']} (类型: {type(result_no_status['auth_status']).__name__})")
                else:
                    print(f"   连不带状态条件的查询也失败了")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    test_database_query()
