2025-08-05 02:10:47,041 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-05 02:10:47,043 [INFO] ✅ 从.env文件加载数据库配置
2025-08-05 02:10:47,043 [INFO] 🚀 USDT授权地址监控器启动
2025-08-05 02:10:47,043 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-05 02:10:47,043 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:10:47,047 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:10:47,059 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:10:47,060 [INFO] 📭 没有需要监控的地址
2025-08-05 02:10:47,060 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:11:47,060 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:11:47,073 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:11:47,076 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:11:47,076 [INFO] 📭 没有需要监控的地址
2025-08-05 02:11:47,076 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:12:47,077 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:12:47,089 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:12:47,092 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:12:47,092 [INFO] 📭 没有需要监控的地址
2025-08-05 02:12:47,092 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:13:47,093 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:13:47,105 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:13:47,118 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:13:47,118 [INFO] 📭 没有需要监控的地址
2025-08-05 02:13:47,118 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:14:47,118 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:14:47,122 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:14:47,125 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:14:47,125 [INFO] 📭 没有需要监控的地址
2025-08-05 02:14:47,125 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:15:47,125 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:15:47,137 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:15:47,141 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:15:47,141 [INFO] 📭 没有需要监控的地址
2025-08-05 02:15:47,141 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:16:47,141 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:16:47,153 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:16:47,165 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:16:47,165 [INFO] 📭 没有需要监控的地址
2025-08-05 02:16:47,165 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:17:47,165 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:17:47,177 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:17:47,189 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:17:47,189 [INFO] 📭 没有需要监控的地址
2025-08-05 02:17:47,189 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:18:47,190 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:18:47,203 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:18:47,206 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:18:47,206 [INFO] 📭 没有需要监控的地址
2025-08-05 02:18:47,207 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:19:47,208 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:19:47,220 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:19:47,232 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:19:47,232 [INFO] 📭 没有需要监控的地址
2025-08-05 02:19:47,232 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:20:47,233 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:20:47,244 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:20:47,249 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:20:47,249 [INFO] 📭 没有需要监控的地址
2025-08-05 02:20:47,249 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:21:47,249 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:21:47,256 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:21:47,263 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:21:47,263 [INFO] 📭 没有需要监控的地址
2025-08-05 02:21:47,263 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:22:47,265 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:22:47,277 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:22:47,281 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:22:47,281 [INFO] 📭 没有需要监控的地址
2025-08-05 02:22:47,281 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:23:47,282 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:23:47,286 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:23:47,289 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:23:47,289 [INFO] 📭 没有需要监控的地址
2025-08-05 02:23:47,289 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:24:47,290 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:24:47,293 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:24:47,305 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:24:47,305 [INFO] 📭 没有需要监控的地址
2025-08-05 02:24:47,305 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:25:47,306 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:25:47,318 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:25:47,322 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:25:47,322 [INFO] 📭 没有需要监控的地址
2025-08-05 02:25:47,322 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:26:47,323 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:26:47,327 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:26:47,330 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:26:47,330 [INFO] 📭 没有需要监控的地址
2025-08-05 02:26:47,330 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:27:47,332 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:27:47,343 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:27:47,355 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:27:47,355 [INFO] 📭 没有需要监控的地址
2025-08-05 02:27:47,355 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:28:47,357 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:28:47,360 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:28:47,363 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:28:47,363 [INFO] 📭 没有需要监控的地址
2025-08-05 02:28:47,363 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:29:47,365 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:29:47,376 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:29:47,388 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:29:47,388 [INFO] 📭 没有需要监控的地址
2025-08-05 02:29:47,388 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:30:47,389 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:30:47,392 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:30:47,403 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:30:47,403 [INFO] 📭 没有需要监控的地址
2025-08-05 02:30:47,404 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:31:18,713 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-05 02:31:18,714 [INFO] ✅ 从.env文件加载数据库配置
2025-08-05 02:31:18,714 [INFO] 🚀 USDT授权地址监控器启动
2025-08-05 02:31:18,715 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-05 02:31:18,715 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:31:18,727 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:31:18,727 [ERROR] � 使用配置: dujiaoka@127.0.0.1:3306/dujiaoka
2025-08-05 02:31:18,731 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'localhost' (using password: YES)")
2025-08-05 02:31:18,731 [ERROR] � 使用配置: dujiaoka@127.0.0.1:3306/dujiaoka
2025-08-05 02:31:18,731 [INFO] 📭 没有需要监控的地址
2025-08-05 02:31:18,731 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:33:58,577 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-05 02:33:58,578 [INFO] ✅ 使用固定数据库配置
2025-08-05 02:33:58,578 [INFO] 🚀 USDT授权地址监控器启动
2025-08-05 02:33:58,578 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-05 02:33:58,578 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:33:58,626 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'*************' (using password: YES)")
2025-08-05 02:33:58,626 [ERROR] � 使用配置: dujiaoka@*************1:3306/dujiaoka
2025-08-05 02:33:58,630 [ERROR] ❌ 数据库连接失败: (1045, "Access denied for user 'dujiaoka'@'*************' (using password: YES)")
2025-08-05 02:33:58,630 [ERROR] � 使用配置: dujiaoka@*************1:3306/dujiaoka
2025-08-05 02:33:58,630 [INFO] 📭 没有需要监控的地址
2025-08-05 02:33:58,631 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:34:23,917 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-05 02:34:23,918 [INFO] ✅ 使用固定数据库配置
2025-08-05 02:34:23,918 [INFO] 🚀 USDT授权地址监控器启动
2025-08-05 02:34:23,919 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-05 02:34:23,919 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:34:23,937 [ERROR] ❌ 获取系统配置失败: 'NoneType' object has no attribute 'split'
2025-08-05 02:34:23,945 [INFO] 📭 没有需要监控的地址
2025-08-05 02:34:23,945 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:35:11,047 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-05 02:35:11,047 [INFO] ✅ 使用固定数据库配置
2025-08-05 02:35:11,047 [INFO] 🚀 USDT授权地址监控器启动
2025-08-05 02:35:11,049 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-05 02:35:11,049 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:35:11,069 [INFO] 📭 没有需要监控的地址
2025-08-05 02:35:11,069 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:36:11,069 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:36:11,103 [INFO] 📭 没有需要监控的地址
2025-08-05 02:36:11,103 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:37:11,104 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:37:11,129 [INFO] 📭 没有需要监控的地址
2025-08-05 02:37:11,130 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:38:11,130 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:38:11,164 [INFO] 📭 没有需要监控的地址
2025-08-05 02:38:11,164 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:39:11,165 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:39:11,191 [INFO] 📭 没有需要监控的地址
2025-08-05 02:39:11,191 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:40:11,192 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:40:11,226 [INFO] 📭 没有需要监控的地址
2025-08-05 02:40:11,226 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:41:11,226 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:41:11,260 [INFO] 📭 没有需要监控的地址
2025-08-05 02:41:11,260 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:42:11,260 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:42:11,296 [INFO] 📭 没有需要监控的地址
2025-08-05 02:42:11,297 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:43:11,297 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:43:11,330 [INFO] 📭 没有需要监控的地址
2025-08-05 02:43:11,330 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:44:11,330 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:44:11,355 [INFO] 📭 没有需要监控的地址
2025-08-05 02:44:11,355 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:45:11,356 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:45:11,388 [INFO] 📭 没有需要监控的地址
2025-08-05 02:45:11,389 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:46:11,389 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:46:11,413 [INFO] 📭 没有需要监控的地址
2025-08-05 02:46:11,413 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:47:11,414 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:47:11,449 [INFO] 📭 没有需要监控的地址
2025-08-05 02:47:11,449 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:48:11,450 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:48:11,483 [INFO] 📭 没有需要监控的地址
2025-08-05 02:48:11,483 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:49:11,484 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:49:11,510 [INFO] 📭 没有需要监控的地址
2025-08-05 02:49:11,511 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:50:11,511 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:50:11,545 [INFO] 📭 没有需要监控的地址
2025-08-05 02:50:11,545 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:51:11,546 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:51:11,560 [INFO] 📭 没有需要监控的地址
2025-08-05 02:51:11,561 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:52:11,562 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:52:11,586 [INFO] 📭 没有需要监控的地址
2025-08-05 02:52:11,586 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:53:11,586 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:53:11,610 [INFO] 📭 没有需要监控的地址
2025-08-05 02:53:11,610 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:54:11,611 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:54:11,637 [INFO] 📭 没有需要监控的地址
2025-08-05 02:54:11,637 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:55:11,638 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:55:11,659 [INFO] 📭 没有需要监控的地址
2025-08-05 02:55:11,659 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:56:11,660 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:56:11,694 [INFO] 📭 没有需要监控的地址
2025-08-05 02:56:11,695 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:57:11,696 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:57:11,711 [INFO] 📭 没有需要监控的地址
2025-08-05 02:57:11,711 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:58:11,711 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:58:11,745 [INFO] 📭 没有需要监控的地址
2025-08-05 02:58:11,745 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 02:59:11,745 [INFO] 🔍 开始执行地址监控任务
2025-08-05 02:59:11,771 [INFO] 📭 没有需要监控的地址
2025-08-05 02:59:11,771 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:00:11,771 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:00:11,808 [INFO] 📭 没有需要监控的地址
2025-08-05 03:00:11,808 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:01:11,809 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:01:11,826 [INFO] 📭 没有需要监控的地址
2025-08-05 03:01:11,826 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:02:11,827 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:02:11,861 [INFO] 📭 没有需要监控的地址
2025-08-05 03:02:11,861 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:03:11,861 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:03:11,895 [INFO] 📭 没有需要监控的地址
2025-08-05 03:03:11,896 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:04:11,897 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:04:11,922 [INFO] 📭 没有需要监控的地址
2025-08-05 03:04:11,922 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:05:11,922 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:05:11,945 [INFO] 📭 没有需要监控的地址
2025-08-05 03:05:11,945 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:06:11,946 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:06:11,980 [INFO] 📭 没有需要监控的地址
2025-08-05 03:06:11,981 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:07:11,981 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:07:12,013 [INFO] 📭 没有需要监控的地址
2025-08-05 03:07:12,013 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:08:12,014 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:08:12,046 [INFO] 📭 没有需要监控的地址
2025-08-05 03:08:12,047 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:09:12,047 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:09:12,081 [INFO] 📭 没有需要监控的地址
2025-08-05 03:09:12,081 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:10:12,081 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:10:12,115 [INFO] 📭 没有需要监控的地址
2025-08-05 03:10:12,115 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:11:12,115 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:11:12,150 [INFO] 📭 没有需要监控的地址
2025-08-05 03:11:12,150 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:12:12,151 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:12:12,184 [INFO] 📭 没有需要监控的地址
2025-08-05 03:12:12,184 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:13:12,184 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:13:12,209 [INFO] 📭 没有需要监控的地址
2025-08-05 03:13:12,209 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:14:12,210 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:14:12,236 [INFO] 📭 没有需要监控的地址
2025-08-05 03:14:12,237 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:15:12,237 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:15:12,272 [INFO] 📭 没有需要监控的地址
2025-08-05 03:15:12,272 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:16:12,273 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:16:12,306 [INFO] 📭 没有需要监控的地址
2025-08-05 03:16:12,306 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:17:12,306 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:17:12,333 [INFO] 📭 没有需要监控的地址
2025-08-05 03:17:12,333 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:18:12,334 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:18:12,369 [INFO] 📭 没有需要监控的地址
2025-08-05 03:18:12,370 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-05 03:19:12,370 [INFO] 🔍 开始执行地址监控任务
2025-08-05 03:19:12,400 [INFO] 📭 没有需要监控的地址
2025-08-05 03:19:12,400 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:09:54,984 [INFO] ✅ 使用代理: http://127.0.0.1:7891
2025-08-06 20:09:54,986 [INFO] ✅ 使用固定数据库配置
2025-08-06 20:09:54,992 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 20:09:54,992 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 20:09:54,992 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 20:09:54,993 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 20:09:54,993 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 20:09:54,993 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:09:55,028 [INFO] 📋 监控地址数量: 2
2025-08-06 20:09:55,028 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:09:55,040 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 20:09:55,041 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 20:09:55,530 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:09:56,030 [INFO] ✅ 地址监控任务完成
2025-08-06 20:09:56,031 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:10:05,881 [INFO] 127.0.0.1 - - [06/Aug/2025 20:10:05] "GET /health HTTP/1.1" 200 -
2025-08-06 20:10:56,031 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:10:56,049 [INFO] 📋 监控地址数量: 2
2025-08-06 20:10:56,049 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:10:56,551 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:10:57,051 [INFO] ✅ 地址监控任务完成
2025-08-06 20:10:57,052 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:11:57,053 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:11:57,070 [INFO] 📋 监控地址数量: 2
2025-08-06 20:11:57,070 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:11:57,571 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:11:58,071 [INFO] ✅ 地址监控任务完成
2025-08-06 20:11:58,072 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:12:58,072 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:12:58,092 [INFO] 📋 监控地址数量: 2
2025-08-06 20:12:58,092 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:12:58,593 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:12:59,095 [INFO] ✅ 地址监控任务完成
2025-08-06 20:12:59,095 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:13:59,096 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:13:59,116 [INFO] 📋 监控地址数量: 2
2025-08-06 20:13:59,117 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:13:59,619 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:14:00,120 [INFO] ✅ 地址监控任务完成
2025-08-06 20:14:00,121 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:15:00,122 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:15:00,156 [INFO] 📋 监控地址数量: 2
2025-08-06 20:15:00,157 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:15:00,658 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:15:01,159 [INFO] ✅ 地址监控任务完成
2025-08-06 20:15:01,159 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:16:01,159 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:16:01,194 [INFO] 📋 监控地址数量: 2
2025-08-06 20:16:01,195 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:16:01,695 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:16:02,196 [INFO] ✅ 地址监控任务完成
2025-08-06 20:16:02,197 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:17:02,197 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:17:02,216 [INFO] 📋 监控地址数量: 2
2025-08-06 20:17:02,216 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:17:02,718 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:17:03,218 [INFO] ✅ 地址监控任务完成
2025-08-06 20:17:03,218 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:18:03,219 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:18:03,235 [INFO] 📋 监控地址数量: 2
2025-08-06 20:18:03,235 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:18:03,736 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:18:04,237 [INFO] ✅ 地址监控任务完成
2025-08-06 20:18:04,237 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:19:04,238 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:19:04,264 [INFO] 📋 监控地址数量: 2
2025-08-06 20:19:04,265 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:19:04,766 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:19:05,266 [INFO] ✅ 地址监控任务完成
2025-08-06 20:19:05,267 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:20:05,267 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:20:05,300 [INFO] 📋 监控地址数量: 2
2025-08-06 20:20:05,301 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:20:05,801 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:20:06,302 [INFO] ✅ 地址监控任务完成
2025-08-06 20:20:06,303 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:21:06,304 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:21:06,326 [INFO] 📋 监控地址数量: 2
2025-08-06 20:21:06,327 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:21:06,828 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:21:07,328 [INFO] ✅ 地址监控任务完成
2025-08-06 20:21:07,329 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:22:07,330 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:22:07,365 [INFO] 📋 监控地址数量: 2
2025-08-06 20:22:07,366 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:22:07,866 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:22:08,367 [INFO] ✅ 地址监控任务完成
2025-08-06 20:22:08,367 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:23:08,367 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:23:08,384 [INFO] 📋 监控地址数量: 2
2025-08-06 20:23:08,384 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:23:08,885 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:23:09,385 [INFO] ✅ 地址监控任务完成
2025-08-06 20:23:09,385 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:24:09,386 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:24:09,402 [INFO] 📋 监控地址数量: 2
2025-08-06 20:24:09,403 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:24:09,903 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:24:10,404 [INFO] ✅ 地址监控任务完成
2025-08-06 20:24:10,405 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:25:10,406 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:25:10,432 [INFO] 📋 监控地址数量: 2
2025-08-06 20:25:10,432 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:25:10,932 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:25:11,433 [INFO] ✅ 地址监控任务完成
2025-08-06 20:25:11,434 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:26:11,434 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:26:11,455 [INFO] 📋 监控地址数量: 2
2025-08-06 20:26:11,455 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:26:11,956 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:26:12,457 [INFO] ✅ 地址监控任务完成
2025-08-06 20:26:12,457 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:27:12,459 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:27:12,494 [INFO] 📋 监控地址数量: 2
2025-08-06 20:27:12,495 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:27:12,995 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:27:13,496 [INFO] ✅ 地址监控任务完成
2025-08-06 20:27:13,496 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:28:13,498 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:28:13,517 [INFO] 📋 监控地址数量: 2
2025-08-06 20:28:13,518 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:28:14,020 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:28:14,521 [INFO] ✅ 地址监控任务完成
2025-08-06 20:28:14,522 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:29:14,524 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:29:14,559 [INFO] 📋 监控地址数量: 2
2025-08-06 20:29:14,559 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:29:15,060 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:29:15,561 [INFO] ✅ 地址监控任务完成
2025-08-06 20:29:15,561 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:30:15,562 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:30:15,581 [INFO] 📋 监控地址数量: 2
2025-08-06 20:30:15,581 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:30:16,082 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:30:16,583 [INFO] ✅ 地址监控任务完成
2025-08-06 20:30:16,583 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:31:16,584 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:31:16,611 [INFO] 📋 监控地址数量: 2
2025-08-06 20:31:16,612 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:31:17,112 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:31:17,614 [INFO] ✅ 地址监控任务完成
2025-08-06 20:31:17,614 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:32:17,615 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:32:17,649 [INFO] 📋 监控地址数量: 2
2025-08-06 20:32:17,650 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:32:18,151 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:32:18,652 [INFO] ✅ 地址监控任务完成
2025-08-06 20:32:18,652 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:33:18,653 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:33:18,669 [INFO] 📋 监控地址数量: 2
2025-08-06 20:33:18,670 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:33:19,171 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:33:19,671 [INFO] ✅ 地址监控任务完成
2025-08-06 20:33:19,671 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:34:19,673 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:34:19,689 [INFO] 📋 监控地址数量: 2
2025-08-06 20:34:19,689 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:34:20,190 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:34:20,690 [INFO] ✅ 地址监控任务完成
2025-08-06 20:34:20,691 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:35:20,692 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:35:20,724 [INFO] 📋 监控地址数量: 2
2025-08-06 20:35:20,724 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:35:21,225 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:35:21,726 [INFO] ✅ 地址监控任务完成
2025-08-06 20:35:21,726 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:36:21,728 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:36:21,762 [INFO] 📋 监控地址数量: 2
2025-08-06 20:36:21,762 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:36:22,263 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:36:22,764 [INFO] ✅ 地址监控任务完成
2025-08-06 20:36:22,765 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:37:22,765 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:37:22,790 [INFO] 📋 监控地址数量: 2
2025-08-06 20:37:22,790 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:37:23,291 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:37:23,792 [INFO] ✅ 地址监控任务完成
2025-08-06 20:37:23,792 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:38:23,793 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:38:23,820 [INFO] 📋 监控地址数量: 2
2025-08-06 20:38:23,821 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:38:24,321 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:38:24,822 [INFO] ✅ 地址监控任务完成
2025-08-06 20:38:24,823 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:39:24,823 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:39:24,840 [INFO] 📋 监控地址数量: 2
2025-08-06 20:39:24,841 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:39:25,342 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:39:25,843 [INFO] ✅ 地址监控任务完成
2025-08-06 20:39:25,844 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:40:25,844 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:40:25,870 [INFO] 📋 监控地址数量: 2
2025-08-06 20:40:25,871 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:40:26,371 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:40:26,872 [INFO] ✅ 地址监控任务完成
2025-08-06 20:40:26,872 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:41:26,874 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:41:26,900 [INFO] 📋 监控地址数量: 2
2025-08-06 20:41:26,900 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:41:27,402 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:41:27,903 [INFO] ✅ 地址监控任务完成
2025-08-06 20:41:27,903 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:42:27,903 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:42:27,939 [INFO] 📋 监控地址数量: 2
2025-08-06 20:42:27,940 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:42:28,441 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:42:28,942 [INFO] ✅ 地址监控任务完成
2025-08-06 20:42:28,942 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:43:28,943 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:43:28,979 [INFO] 📋 监控地址数量: 2
2025-08-06 20:43:28,979 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:43:29,480 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:43:29,980 [INFO] ✅ 地址监控任务完成
2025-08-06 20:43:29,981 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:44:29,981 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:44:30,015 [INFO] 📋 监控地址数量: 2
2025-08-06 20:44:30,016 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:44:30,517 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:44:31,018 [INFO] ✅ 地址监控任务完成
2025-08-06 20:44:31,018 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:45:31,020 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:45:31,037 [INFO] 📋 监控地址数量: 2
2025-08-06 20:45:31,037 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:45:31,538 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:45:32,039 [INFO] ✅ 地址监控任务完成
2025-08-06 20:45:32,040 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:46:32,040 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:46:32,078 [INFO] 📋 监控地址数量: 2
2025-08-06 20:46:32,078 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:46:32,579 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:46:33,080 [INFO] ✅ 地址监控任务完成
2025-08-06 20:46:33,080 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:47:33,082 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:47:33,117 [INFO] 📋 监控地址数量: 2
2025-08-06 20:47:33,118 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:47:33,619 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:47:34,120 [INFO] ✅ 地址监控任务完成
2025-08-06 20:47:34,120 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:48:34,121 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:48:34,137 [INFO] 📋 监控地址数量: 2
2025-08-06 20:48:34,138 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:48:34,639 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:48:35,140 [INFO] ✅ 地址监控任务完成
2025-08-06 20:48:35,140 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:49:35,140 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:49:35,176 [INFO] 📋 监控地址数量: 2
2025-08-06 20:49:35,177 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:49:35,678 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:49:36,179 [INFO] ✅ 地址监控任务完成
2025-08-06 20:49:36,179 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:50:36,179 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:50:36,207 [INFO] 📋 监控地址数量: 2
2025-08-06 20:50:36,208 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:50:36,708 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:50:37,209 [INFO] ✅ 地址监控任务完成
2025-08-06 20:50:37,210 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:51:37,211 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:51:37,235 [INFO] 📋 监控地址数量: 2
2025-08-06 20:51:37,237 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:51:37,737 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:51:38,238 [INFO] ✅ 地址监控任务完成
2025-08-06 20:51:38,238 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:52:38,238 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:52:38,273 [INFO] 📋 监控地址数量: 2
2025-08-06 20:52:38,274 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:52:38,775 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:52:39,275 [INFO] ✅ 地址监控任务完成
2025-08-06 20:52:39,275 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:53:39,276 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:53:39,310 [INFO] 📋 监控地址数量: 2
2025-08-06 20:53:39,311 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:53:39,812 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:53:40,313 [INFO] ✅ 地址监控任务完成
2025-08-06 20:53:40,313 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:54:40,313 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:54:40,342 [INFO] 📋 监控地址数量: 2
2025-08-06 20:54:40,342 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:54:40,842 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:54:41,343 [INFO] ✅ 地址监控任务完成
2025-08-06 20:54:41,344 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:55:41,345 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:55:41,381 [INFO] 📋 监控地址数量: 2
2025-08-06 20:55:41,382 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:55:41,882 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:55:42,382 [INFO] ✅ 地址监控任务完成
2025-08-06 20:55:42,382 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:56:42,383 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:56:42,410 [INFO] 📋 监控地址数量: 2
2025-08-06 20:56:42,410 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:56:42,912 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:56:43,412 [INFO] ✅ 地址监控任务完成
2025-08-06 20:56:43,412 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:57:43,412 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:57:43,436 [INFO] 📋 监控地址数量: 2
2025-08-06 20:57:43,437 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:57:43,937 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:57:44,437 [INFO] ✅ 地址监控任务完成
2025-08-06 20:57:44,438 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:58:44,439 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:58:44,455 [INFO] 📋 监控地址数量: 2
2025-08-06 20:58:44,456 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:58:44,956 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:58:45,457 [INFO] ✅ 地址监控任务完成
2025-08-06 20:58:45,458 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 20:59:45,459 [INFO] 🔍 开始执行地址监控任务
2025-08-06 20:59:45,492 [INFO] 📋 监控地址数量: 2
2025-08-06 20:59:45,492 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 20:59:45,993 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 20:59:46,494 [INFO] ✅ 地址监控任务完成
2025-08-06 20:59:46,494 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:00:46,495 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:00:46,535 [INFO] 📋 监控地址数量: 2
2025-08-06 21:00:46,535 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:00:47,036 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:00:47,537 [INFO] ✅ 地址监控任务完成
2025-08-06 21:00:47,537 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:01:47,537 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:01:47,557 [INFO] 📋 监控地址数量: 2
2025-08-06 21:01:47,558 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:01:48,059 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:01:48,559 [INFO] ✅ 地址监控任务完成
2025-08-06 21:01:48,559 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:02:48,561 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:02:48,595 [INFO] 📋 监控地址数量: 2
2025-08-06 21:02:48,595 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:02:49,097 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:02:49,598 [INFO] ✅ 地址监控任务完成
2025-08-06 21:02:49,598 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:03:49,599 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:03:49,624 [INFO] 📋 监控地址数量: 2
2025-08-06 21:03:49,624 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:03:50,125 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:03:50,626 [INFO] ✅ 地址监控任务完成
2025-08-06 21:03:50,627 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:04:50,627 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:04:50,654 [INFO] 📋 监控地址数量: 2
2025-08-06 21:04:50,655 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:04:51,156 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:04:51,656 [INFO] ✅ 地址监控任务完成
2025-08-06 21:04:51,657 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:05:51,658 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:05:51,674 [INFO] 📋 监控地址数量: 2
2025-08-06 21:05:51,674 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:05:52,175 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:05:52,676 [INFO] ✅ 地址监控任务完成
2025-08-06 21:05:52,676 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:06:52,678 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:06:52,714 [INFO] 📋 监控地址数量: 2
2025-08-06 21:06:52,714 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:06:53,214 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:06:53,715 [INFO] ✅ 地址监控任务完成
2025-08-06 21:06:53,716 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:07:53,716 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:07:53,742 [INFO] 📋 监控地址数量: 2
2025-08-06 21:07:53,742 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:07:54,243 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:07:54,744 [INFO] ✅ 地址监控任务完成
2025-08-06 21:07:54,744 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:08:54,745 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:08:54,786 [INFO] 📋 监控地址数量: 2
2025-08-06 21:08:54,786 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:08:55,287 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:08:55,788 [INFO] ✅ 地址监控任务完成
2025-08-06 21:08:55,788 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:09:55,789 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:09:55,815 [INFO] 📋 监控地址数量: 2
2025-08-06 21:09:55,815 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:09:56,315 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:09:56,816 [INFO] ✅ 地址监控任务完成
2025-08-06 21:09:56,816 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:10:56,817 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:10:56,835 [INFO] 📋 监控地址数量: 2
2025-08-06 21:10:56,836 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:10:57,336 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:10:57,836 [INFO] ✅ 地址监控任务完成
2025-08-06 21:10:57,839 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:11:43,179 [INFO] ⚠️ 代理不可用，使用直连模式
2025-08-06 21:11:43,180 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:11:43,185 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:11:43,185 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:11:43,185 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:11:43,185 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:11:43,186 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:11:43,186 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:11:43,222 [INFO] 📋 监控地址数量: 2
2025-08-06 21:11:43,222 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:11:43,224 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 21:11:43,225 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:11:43,723 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:11:44,223 [INFO] ✅ 地址监控任务完成
2025-08-06 21:11:44,224 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:11:57,839 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:11:57,867 [INFO] 📋 监控地址数量: 2
2025-08-06 21:11:57,868 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:11:58,368 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:11:58,868 [INFO] ✅ 地址监控任务完成
2025-08-06 21:11:58,869 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:12:44,224 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:12:44,261 [INFO] 📋 监控地址数量: 2
2025-08-06 21:12:44,261 [WARNING] ⚠️ 获取地址余额失败: Tgw6zho1cjhc
2025-08-06 21:12:44,762 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:12:45,263 [INFO] ✅ 地址监控任务完成
2025-08-06 21:12:45,263 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:12:58,870 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:12:58,894 [INFO] 📋 监控地址数量: 1
2025-08-06 21:12:58,895 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:12:59,396 [INFO] ✅ 地址监控任务完成
2025-08-06 21:12:59,396 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:13:45,264 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:13:45,279 [INFO] 📋 监控地址数量: 1
2025-08-06 21:13:45,280 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:13:45,780 [INFO] ✅ 地址监控任务完成
2025-08-06 21:13:45,780 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:13:59,396 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:13:59,413 [INFO] 📋 监控地址数量: 1
2025-08-06 21:13:59,414 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:13:59,914 [INFO] ✅ 地址监控任务完成
2025-08-06 21:13:59,915 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:14:45,780 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:14:45,797 [INFO] 📋 监控地址数量: 1
2025-08-06 21:14:45,797 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:14:46,298 [INFO] ✅ 地址监控任务完成
2025-08-06 21:14:46,298 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:14:59,916 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:14:59,941 [INFO] 📋 监控地址数量: 1
2025-08-06 21:14:59,941 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:15:00,442 [INFO] ✅ 地址监控任务完成
2025-08-06 21:15:00,443 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:15:46,298 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:15:46,334 [INFO] 📋 监控地址数量: 1
2025-08-06 21:15:46,334 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:15:46,834 [INFO] ✅ 地址监控任务完成
2025-08-06 21:15:46,834 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:16:00,443 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:16:00,466 [INFO] 📋 监控地址数量: 1
2025-08-06 21:16:00,466 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:16:00,967 [INFO] ✅ 地址监控任务完成
2025-08-06 21:16:00,968 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:16:46,835 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:16:46,864 [INFO] 📋 监控地址数量: 1
2025-08-06 21:16:46,864 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:16:47,364 [INFO] ✅ 地址监控任务完成
2025-08-06 21:16:47,364 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:17:00,968 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:17:00,981 [INFO] 📋 监控地址数量: 1
2025-08-06 21:17:00,981 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:17:01,483 [INFO] ✅ 地址监控任务完成
2025-08-06 21:17:01,483 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:17:47,364 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:17:47,390 [INFO] 📋 监控地址数量: 1
2025-08-06 21:17:47,390 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:17:47,890 [INFO] ✅ 地址监控任务完成
2025-08-06 21:17:47,890 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:18:01,484 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:18:01,503 [INFO] 📋 监控地址数量: 1
2025-08-06 21:18:01,504 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:18:02,004 [INFO] ✅ 地址监控任务完成
2025-08-06 21:18:02,005 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:18:47,891 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:18:47,908 [INFO] 📋 监控地址数量: 1
2025-08-06 21:18:47,908 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:18:48,409 [INFO] ✅ 地址监控任务完成
2025-08-06 21:18:48,409 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:19:02,006 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:19:02,026 [INFO] 📋 监控地址数量: 1
2025-08-06 21:19:02,027 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:19:02,527 [INFO] ✅ 地址监控任务完成
2025-08-06 21:19:02,527 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:19:48,410 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:19:48,443 [INFO] 📋 监控地址数量: 1
2025-08-06 21:19:48,443 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:19:48,943 [INFO] ✅ 地址监控任务完成
2025-08-06 21:19:48,943 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:20:02,529 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:20:02,554 [INFO] 📋 监控地址数量: 1
2025-08-06 21:20:02,555 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:20:03,055 [INFO] ✅ 地址监控任务完成
2025-08-06 21:20:03,055 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:20:48,944 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:20:48,958 [INFO] 📋 监控地址数量: 1
2025-08-06 21:20:48,959 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:20:49,460 [INFO] ✅ 地址监控任务完成
2025-08-06 21:20:49,460 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:21:03,055 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:21:03,080 [INFO] 📋 监控地址数量: 1
2025-08-06 21:21:03,080 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:21:03,581 [INFO] ✅ 地址监控任务完成
2025-08-06 21:21:03,581 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:21:49,460 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:21:49,478 [INFO] 📋 监控地址数量: 1
2025-08-06 21:21:49,478 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:21:49,979 [INFO] ✅ 地址监控任务完成
2025-08-06 21:21:49,979 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:22:03,582 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:22:03,610 [INFO] 📋 监控地址数量: 1
2025-08-06 21:22:03,611 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:22:04,111 [INFO] ✅ 地址监控任务完成
2025-08-06 21:22:04,111 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:22:49,980 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:22:50,013 [INFO] 📋 监控地址数量: 1
2025-08-06 21:22:50,014 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:22:50,514 [INFO] ✅ 地址监控任务完成
2025-08-06 21:22:50,514 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:23:02,504 [INFO] ⚠️ 代理不可用，使用直连模式
2025-08-06 21:23:02,504 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:23:02,511 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:23:02,511 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:23:02,511 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:23:02,511 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:23:02,511 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:23:02,512 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:23:02,532 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:23:02,547 [INFO] 📋 监控地址数量: 1
2025-08-06 21:23:02,548 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:23:02,548 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 21:23:02,549 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:23:02,692 [ERROR] ❌ 获取USDT余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /wallet/triggerconstantcontract (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))
2025-08-06 21:23:02,825 [ERROR] ❌ 获取TRX余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))
2025-08-06 21:23:02,826 [WARNING] ⚠️ API密钥 1 查询失败: USDT=None, TRX=None
2025-08-06 21:23:02,826 [ERROR] ❌ 所有API密钥都查询失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:23:02,826 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:23:03,326 [INFO] ✅ 地址监控任务完成
2025-08-06 21:23:03,326 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:23:04,112 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:23:04,147 [INFO] 📋 监控地址数量: 1
2025-08-06 21:23:05,860 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:23:06,361 [INFO] ✅ 地址监控任务完成
2025-08-06 21:23:06,361 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:23:34,885 [INFO] ⚠️ 代理不可用，使用直连模式
2025-08-06 21:23:34,887 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:23:34,892 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:23:34,892 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:23:34,893 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:23:34,893 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:23:34,893 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:23:34,894 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:23:34,913 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:23:34,929 [INFO] 📋 监控地址数量: 1
2025-08-06 21:23:34,930 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:23:34,933 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:23:34,934 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:23:35,044 [ERROR] ❌ 获取USDT余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /wallet/triggerconstantcontract (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))
2025-08-06 21:23:35,151 [ERROR] ❌ 获取TRX余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))
2025-08-06 21:23:35,151 [WARNING] ⚠️ API密钥 1 查询失败: USDT=None, TRX=None
2025-08-06 21:23:35,152 [ERROR] ❌ 所有API密钥都查询失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:23:35,152 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:23:35,653 [INFO] ✅ 地址监控任务完成
2025-08-06 21:23:35,653 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:24:07,640 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:24:07,654 [INFO] 📋 监控地址数量: 1
2025-08-06 21:24:09,553 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:24:10,055 [INFO] ✅ 地址监控任务完成
2025-08-06 21:24:10,056 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:25:10,057 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:25:10,091 [INFO] 📋 监控地址数量: 1
2025-08-06 21:25:11,635 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:25:12,136 [INFO] ✅ 地址监控任务完成
2025-08-06 21:25:12,136 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:26:12,138 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:26:12,163 [INFO] 📋 监控地址数量: 1
2025-08-06 21:26:14,180 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:26:14,680 [INFO] ✅ 地址监控任务完成
2025-08-06 21:26:14,680 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:27:14,682 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:27:14,702 [INFO] 📋 监控地址数量: 1
2025-08-06 21:27:16,389 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:27:16,890 [INFO] ✅ 地址监控任务完成
2025-08-06 21:27:16,890 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:28:16,891 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:28:16,916 [INFO] 📋 监控地址数量: 1
2025-08-06 21:28:18,470 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:28:18,971 [INFO] ✅ 地址监控任务完成
2025-08-06 21:28:18,971 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:28:52,285 [INFO] 127.0.0.1 - - [06/Aug/2025 21:28:52] "GET /health HTTP/1.1" 200 -
2025-08-06 21:28:52,296 [INFO] 🔔 HTTP触发立即检查地址: Tgw6zho1cjhc
2025-08-06 21:28:52,333 [WARNING] ⚠️ 地址未找到或未授权: Tgw6zho1cjhc
2025-08-06 21:28:52,334 [INFO] 127.0.0.1 - - [06/Aug/2025 21:28:52] "[35m[1mPOST /trigger_check HTTP/1.1[0m" 500 -
2025-08-06 21:28:52,427 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:28:52,427 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:28:52,431 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:28:52,431 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:28:52,431 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:28:52,431 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:28:52,432 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:28:52,432 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:28:52,447 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:28:52,463 [INFO] 📋 监控地址数量: 1
2025-08-06 21:28:52,463 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:28:52,466 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:28:52,467 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:28:53,161 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:28:53,683 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:28:53,701 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:28:54,203 [INFO] ✅ 地址监控任务完成
2025-08-06 21:28:54,203 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:29:18,972 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:29:18,992 [INFO] 📋 监控地址数量: 1
2025-08-06 21:29:20,547 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:29:21,048 [INFO] ✅ 地址监控任务完成
2025-08-06 21:29:21,048 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:29:36,967 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:29:36,968 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:29:36,972 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:29:36,972 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:29:36,972 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:29:36,973 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:29:36,973 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:29:36,973 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:29:36,990 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:29:37,006 [INFO] 📋 监控地址数量: 1
2025-08-06 21:29:37,006 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:29:37,009 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:29:37,010 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:29:38,566 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:29:39,283 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:29:39,302 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:29:39,803 [INFO] ✅ 地址监控任务完成
2025-08-06 21:29:39,803 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:29:54,204 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:29:54,221 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:29:54,238 [INFO] 📋 监控地址数量: 1
2025-08-06 21:29:54,238 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:29:54,593 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:29:55,453 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:29:55,480 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:29:55,982 [INFO] ✅ 地址监控任务完成
2025-08-06 21:29:55,983 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:30:21,049 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:30:21,074 [INFO] 📋 监控地址数量: 1
2025-08-06 21:30:22,714 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:30:23,215 [INFO] ✅ 地址监控任务完成
2025-08-06 21:30:23,215 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:30:37,338 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:30:37,339 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:30:37,343 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:30:37,343 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:30:37,344 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:30:37,344 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:30:37,344 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:30:37,344 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:30:37,364 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:30:37,385 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:30:37,385 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:30:37,391 [INFO] 📋 监控地址数量: 1
2025-08-06 21:30:37,391 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:30:37,392 [WARNING] ⚠️ 无效的地址格式: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:30:38,019 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:30:38,035 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:30:38,537 [INFO] ✅ 地址监控任务完成
2025-08-06 21:30:38,537 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:30:55,983 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:30:56,001 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:30:56,009 [INFO] 📋 监控地址数量: 1
2025-08-06 21:30:56,009 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:30:56,426 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:30:57,075 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:30:57,093 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:30:57,593 [INFO] ✅ 地址监控任务完成
2025-08-06 21:30:57,595 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:31:23,216 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:31:23,245 [INFO] 📋 监控地址数量: 1
2025-08-06 21:31:24,982 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:31:25,483 [INFO] ✅ 地址监控任务完成
2025-08-06 21:31:25,483 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:31:38,538 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:31:38,554 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:31:38,570 [INFO] 📋 监控地址数量: 1
2025-08-06 21:31:38,570 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:31:38,570 [WARNING] ⚠️ 无效的地址格式: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:31:39,544 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:31:39,569 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:31:40,070 [INFO] ✅ 地址监控任务完成
2025-08-06 21:31:40,070 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:32:06,101 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:32:06,102 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:32:06,107 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:32:06,108 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:32:06,108 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:32:06,109 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:32:06,109 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:32:06,109 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:32:06,134 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:32:06,156 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:32:06,156 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:32:06,157 [INFO] 📋 监控地址数量: 1
2025-08-06 21:32:06,157 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:32:06,720 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:32:06.601+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:32:06,879 [INFO] 127.0.0.1 - - [06/Aug/2025 21:32:06] "GET /health HTTP/1.1" 200 -
2025-08-06 21:32:06,898 [INFO] 🔔 HTTP触发立即检查地址: Tgw6zho1cjhc
2025-08-06 21:32:06,919 [WARNING] ⚠️ 地址未找到或未授权: Tgw6zho1cjhc
2025-08-06 21:32:06,920 [INFO] 127.0.0.1 - - [06/Aug/2025 21:32:06] "[35m[1mPOST /trigger_check HTTP/1.1[0m" 500 -
2025-08-06 21:32:07,404 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:32:07,418 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:32:07,919 [INFO] ✅ 地址监控任务完成
2025-08-06 21:32:07,919 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:33:07,920 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:33:07,945 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:33:07,971 [INFO] 📋 监控地址数量: 1
2025-08-06 21:33:07,973 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:33:08,608 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:33:08.474+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:33:09,520 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:33:09,551 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:33:10,052 [INFO] ✅ 地址监控任务完成
2025-08-06 21:33:10,053 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:34:10,055 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:34:10,062 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:34:10,080 [INFO] 📋 监控地址数量: 1
2025-08-06 21:34:10,081 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:34:10,906 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:34:10.592+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:34:11,951 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:34:11,985 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:34:12,485 [INFO] ✅ 地址监控任务完成
2025-08-06 21:34:12,485 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:35:12,487 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:35:12,496 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:35:12,514 [INFO] 📋 监控地址数量: 1
2025-08-06 21:35:12,514 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:35:13,494 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:35:13.015+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:35:15,436 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:35:15,466 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:35:15,969 [INFO] ✅ 地址监控任务完成
2025-08-06 21:35:15,970 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:36:08,136 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:36:08,137 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:36:08,140 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:36:08,140 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:36:08,140 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:36:08,140 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:36:08,141 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:36:08,141 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:36:08,158 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:36:08,176 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:36:08,176 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:36:08,182 [INFO] 📋 监控地址数量: 1
2025-08-06 21:36:08,182 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:36:08,781 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:36:08.670+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:36:10,354 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:36:10,370 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:36:10,870 [INFO] ✅ 地址监控任务完成
2025-08-06 21:36:10,870 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:36:15,971 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:36:15,989 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:36:16,006 [INFO] 📋 监控地址数量: 1
2025-08-06 21:36:16,006 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:36:26,733 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:36:26.633+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens/trc20/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}
2025-08-06 21:36:27,642 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:36:27,670 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:36:28,171 [INFO] ✅ 地址监控任务完成
2025-08-06 21:36:28,172 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:37:08,059 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:37:08,060 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:37:08,064 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:37:08,065 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:37:08,065 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:37:08,065 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:37:08,065 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:37:08,066 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:37:08,086 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:37:08,094 [INFO] 📋 监控地址数量: 1
2025-08-06 21:37:08,094 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:37:08,110 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:37:08,111 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:37:08,593 [INFO] 127.0.0.1 - - [06/Aug/2025 21:37:08] "GET /health HTTP/1.1" 200 -
2025-08-06 21:37:08,604 [INFO] 🔔 HTTP触发立即检查地址: Tgw6zho1cjhc
2025-08-06 21:37:08,637 [WARNING] ⚠️ 地址未找到或未授权: Tgw6zho1cjhc
2025-08-06 21:37:08,638 [INFO] 127.0.0.1 - - [06/Aug/2025 21:37:08] "[35m[1mPOST /trigger_check HTTP/1.1[0m" 500 -
2025-08-06 21:37:09,060 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:37:08.950+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:37:11,014 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:37:11,040 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:37:11,541 [INFO] ✅ 地址监控任务完成
2025-08-06 21:37:11,541 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:37:23,679 [INFO] 127.0.0.1 - - [06/Aug/2025 21:37:23] "GET /health HTTP/1.1" 200 -
2025-08-06 21:37:35,376 [INFO] 127.0.0.1 - - [06/Aug/2025 21:37:35] "GET /health HTTP/1.1" 200 -
2025-08-06 21:38:11,542 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:38:11,551 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:38:11,567 [INFO] 📋 监控地址数量: 1
2025-08-06 21:38:11,568 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:38:12,450 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:38:12.348+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:38:14,002 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:38:14,019 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:38:14,521 [INFO] ✅ 地址监控任务完成
2025-08-06 21:38:14,521 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:39:14,522 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:39:14,530 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:39:14,547 [INFO] 📋 监控地址数量: 1
2025-08-06 21:39:14,548 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:39:15,251 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:39:15.154+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:39:15,851 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:39:15,873 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:39:16,374 [INFO] ✅ 地址监控任务完成
2025-08-06 21:39:16,375 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:40:16,375 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:40:16,385 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:40:16,403 [INFO] 📋 监控地址数量: 1
2025-08-06 21:40:16,404 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:40:16,934 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:40:16.840+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:40:17,908 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:40:17,941 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:40:18,441 [INFO] ✅ 地址监控任务完成
2025-08-06 21:40:18,441 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:41:18,443 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:41:18,459 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:41:18,468 [INFO] 📋 监控地址数量: 1
2025-08-06 21:41:18,468 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:41:20,456 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:41:20.346+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:41:21,426 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:41:21,449 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:41:21,950 [INFO] ✅ 地址监控任务完成
2025-08-06 21:41:21,950 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:42:21,950 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:42:21,959 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:42:21,968 [INFO] 📋 监控地址数量: 1
2025-08-06 21:42:21,969 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:42:22,598 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:42:22.414+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:42:23,175 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:42:23,193 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:42:23,694 [INFO] ✅ 地址监控任务完成
2025-08-06 21:42:23,694 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:42:28,931 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:42:28,931 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:42:28,934 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:42:28,935 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:42:28,935 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:42:28,935 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:42:28,935 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:42:28,935 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:42:28,955 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:42:28,972 [INFO] 📋 监控地址数量: 1
2025-08-06 21:42:28,972 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:42:28,972 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:42:28,973 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:42:29,518 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:42:29.406+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:42:31,139 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:42:31,164 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:42:31,665 [INFO] ✅ 地址监控任务完成
2025-08-06 21:42:31,665 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:43:23,695 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:43:23,703 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:43:23,720 [INFO] 📋 监控地址数量: 1
2025-08-06 21:43:23,720 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:43:24,264 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:43:24.167+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:43:24,869 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:43:24,897 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:43:25,398 [INFO] ✅ 地址监控任务完成
2025-08-06 21:43:25,399 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:43:31,666 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:43:31,682 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:43:31,699 [INFO] 📋 监控地址数量: 1
2025-08-06 21:43:31,699 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:43:32,364 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:43:32.184+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:43:33,115 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:43:33,140 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:43:33,641 [INFO] ✅ 地址监控任务完成
2025-08-06 21:43:33,641 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:44:25,399 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:44:25,416 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:44:25,435 [INFO] 📋 监控地址数量: 1
2025-08-06 21:44:25,436 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:44:25,993 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:44:25.900+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:44:26,629 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:44:26,662 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:44:27,163 [INFO] ✅ 地址监控任务完成
2025-08-06 21:44:27,163 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:44:33,642 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:44:33,658 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:44:33,667 [INFO] 📋 监控地址数量: 1
2025-08-06 21:44:33,667 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:44:34,582 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:44:34.487+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:44:35,384 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:44:35,407 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:44:35,908 [INFO] ✅ 地址监控任务完成
2025-08-06 21:44:35,908 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:45:03,732 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:45:03,732 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:45:03,736 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:45:03,736 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:45:03,736 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:45:03,736 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:45:03,736 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:45:03,737 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:45:03,755 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:45:03,771 [INFO] 📋 监控地址数量: 1
2025-08-06 21:45:03,771 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:45:03,771 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:45:03,771 [WARNING] ⚠️ 地址格式异常，尝试直接使用: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:45:03,771 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:45:04,295 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:45:05,018 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:45:05,042 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:45:05,543 [INFO] ✅ 地址监控任务完成
2025-08-06 21:45:05,543 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:45:27,164 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:45:27,182 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:45:27,199 [INFO] 📋 监控地址数量: 1
2025-08-06 21:45:27,200 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:45:27,773 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:45:27.617+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:45:28,420 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:45:28,446 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:45:28,948 [INFO] ✅ 地址监控任务完成
2025-08-06 21:45:28,948 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:46:05,543 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:46:05,561 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:46:05,567 [INFO] 📋 监控地址数量: 1
2025-08-06 21:46:05,567 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:46:05,567 [WARNING] ⚠️ 地址格式异常，尝试直接使用: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:46:05,956 [WARNING] ⚠️ USDT查询响应异常: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-06 21:46:06,507 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:46:06,529 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:46:07,029 [INFO] ✅ 地址监控任务完成
2025-08-06 21:46:07,029 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:46:28,949 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:46:28,958 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:46:28,966 [INFO] 📋 监控地址数量: 1
2025-08-06 21:46:28,967 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:46:29,578 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:46:29.447+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:46:30,110 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:46:30,127 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:46:30,627 [INFO] ✅ 地址监控任务完成
2025-08-06 21:46:30,627 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:46:42,877 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:46:42,878 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:46:42,881 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:46:42,881 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:46:42,881 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:46:42,881 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:46:42,882 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:46:42,882 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:46:42,898 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:46:42,913 [INFO] 📋 监控地址数量: 1
2025-08-06 21:46:42,913 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:46:42,916 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 21:46:42,916 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:46:43,842 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:46:43.657+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:46:44,427 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:46:44,440 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:46:44,940 [INFO] ✅ 地址监控任务完成
2025-08-06 21:46:44,940 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:47:30,629 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:47:30,637 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:47:30,642 [INFO] 📋 监控地址数量: 1
2025-08-06 21:47:30,643 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:47:31,152 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:47:31.061+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:47:32,004 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:47:32,017 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:47:32,517 [INFO] ✅ 地址监控任务完成
2025-08-06 21:47:32,518 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:47:44,940 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:47:44,957 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:47:44,975 [INFO] 📋 监控地址数量: 1
2025-08-06 21:47:44,975 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:47:45,479 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:47:45.385+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:47:46,124 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:47:46,153 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:47:46,655 [INFO] ✅ 地址监控任务完成
2025-08-06 21:47:46,655 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:48:32,519 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:48:32,527 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:48:32,535 [INFO] 📋 监控地址数量: 1
2025-08-06 21:48:32,535 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:48:33,753 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:48:33.662+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:48:34,345 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:48:34,380 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:48:34,882 [INFO] ✅ 地址监控任务完成
2025-08-06 21:48:34,882 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:48:46,656 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:48:46,665 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:48:46,675 [INFO] 📋 监控地址数量: 1
2025-08-06 21:48:46,675 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:48:47,373 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:48:47.192+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:48:47,895 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:48:47,922 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:48:48,423 [INFO] ✅ 地址监控任务完成
2025-08-06 21:48:48,423 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:49:34,883 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:49:34,902 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:49:34,909 [INFO] 📋 监控地址数量: 1
2025-08-06 21:49:34,910 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:49:45,039 [ERROR] ❌ 获取USDT余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Read timed out. (read timeout=10)
2025-08-06 21:49:47,733 [WARNING] ⚠️ API密钥 1 查询失败: USDT=None, TRX=362.***********-08-06 21:49:47,733 [ERROR] ❌ 所有API密钥都查询失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:49:47,734 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:49:48,235 [INFO] ✅ 地址监控任务完成
2025-08-06 21:49:48,235 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:49:48,424 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:49:48,434 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:49:48,442 [INFO] 📋 监控地址数量: 1
2025-08-06 21:49:48,442 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:49:49,246 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:49:48.930+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:49:59,392 [ERROR] ❌ 获取TRX余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Read timed out. (read timeout=10)
2025-08-06 21:49:59,393 [WARNING] ⚠️ API密钥 1 查询失败: USDT=0.0, TRX=None
2025-08-06 21:49:59,393 [ERROR] ❌ 所有API密钥都查询失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:49:59,393 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 21:49:59,893 [INFO] ✅ 地址监控任务完成
2025-08-06 21:49:59,893 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:50:48,236 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:50:48,253 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:50:48,270 [INFO] 📋 监控地址数量: 1
2025-08-06 21:50:48,270 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:50:48,771 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:50:48.690+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:50:49,494 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:50:49,511 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:50:50,012 [INFO] ✅ 地址监控任务完成
2025-08-06 21:50:50,012 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:50:59,893 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:50:59,911 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:50:59,927 [INFO] 📋 监控地址数量: 1
2025-08-06 21:50:59,927 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:51:00,555 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:51:00.438+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:51:01,110 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:51:01,135 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:51:01,636 [INFO] ✅ 地址监控任务完成
2025-08-06 21:51:01,636 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:51:26,848 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:51:26,849 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:51:26,852 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:51:26,853 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:51:26,853 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:51:26,853 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:51:26,853 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:51:26,853 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:51:26,868 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:51:26,885 [INFO] 📋 监控地址数量: 1
2025-08-06 21:51:26,885 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:51:26,885 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 21:51:26,885 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:51:27,423 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:51:27.313+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:51:27,976 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:51:27,998 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:51:28,499 [INFO] ✅ 地址监控任务完成
2025-08-06 21:51:28,499 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:51:50,014 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:51:50,022 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:51:50,040 [INFO] 📋 监控地址数量: 1
2025-08-06 21:51:50,040 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:51:50,603 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:51:50.494+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:51:51,187 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:51:51,212 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:51:51,713 [INFO] ✅ 地址监控任务完成
2025-08-06 21:51:51,713 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:52:19,954 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:52:19,955 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:52:19,959 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:52:19,959 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:52:19,960 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:52:19,960 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:52:19,960 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:52:19,960 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:52:19,980 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:52:19,999 [INFO] 📋 监控地址数量: 1
2025-08-06 21:52:20,000 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:52:20,003 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 21:52:20,003 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:52:20,607 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:52:20.487+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:52:21,195 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:52:21,214 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:52:21,715 [INFO] ✅ 地址监控任务完成
2025-08-06 21:52:21,715 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:52:51,714 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:52:51,732 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:52:51,741 [INFO] 📋 监控地址数量: 1
2025-08-06 21:52:51,741 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:52:55,469 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:52:53.596+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:52:56,227 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:52:56,244 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:52:56,746 [INFO] ✅ 地址监控任务完成
2025-08-06 21:52:56,746 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:53:56,746 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:53:56,764 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:53:56,783 [INFO] 📋 监控地址数量: 1
2025-08-06 21:53:56,784 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:53:57,380 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:53:57.270+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:53:57,953 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:53:57,974 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:53:58,474 [INFO] ✅ 地址监控任务完成
2025-08-06 21:53:58,475 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:54:58,475 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:54:58,485 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:54:58,502 [INFO] 📋 监控地址数量: 1
2025-08-06 21:54:58,502 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:54:59,074 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:54:58.966+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:54:59,712 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:54:59,742 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:55:00,243 [INFO] ✅ 地址监控任务完成
2025-08-06 21:55:00,243 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:56:00,244 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:56:00,253 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:56:00,270 [INFO] 📋 监控地址数量: 1
2025-08-06 21:56:00,270 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:56:01,503 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:56:01.404+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:56:02,236 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:56:02,253 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:56:02,753 [INFO] ✅ 地址监控任务完成
2025-08-06 21:56:02,754 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:56:52,619 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 21:56:52,620 [INFO] ✅ 使用固定数据库配置
2025-08-06 21:56:52,624 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 21:56:52,624 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 21:56:52,625 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 21:56:52,625 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 21:56:52,625 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 21:56:52,625 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:56:52,644 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:56:52,652 [INFO] 📋 监控地址数量: 1
2025-08-06 21:56:52,652 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:56:52,797 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 21:56:52,797 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 21:56:53,222 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:56:53.128+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/tokens"}
2025-08-06 21:56:53,753 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:56:53,783 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:56:54,284 [INFO] ✅ 地址监控任务完成
2025-08-06 21:56:54,284 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:57:02,754 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:57:02,773 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:57:02,783 [INFO] 📋 监控地址数量: 1
2025-08-06 21:57:02,783 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:57:03,380 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:57:03.265+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:57:04,022 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:57:04,051 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:57:04,551 [INFO] ✅ 地址监控任务完成
2025-08-06 21:57:04,552 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:58:04,552 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:58:04,561 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:58:04,569 [INFO] 📋 监控地址数量: 1
2025-08-06 21:58:04,570 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:58:05,429 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:58:05.339+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:58:07,525 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:58:07,551 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:58:08,051 [INFO] ✅ 地址监控任务完成
2025-08-06 21:58:08,051 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 21:59:08,052 [INFO] 🔍 开始执行地址监控任务
2025-08-06 21:59:08,069 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 21:59:08,078 [INFO] 📋 监控地址数量: 1
2025-08-06 21:59:08,078 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 21:59:08,603 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T13:59:08.514+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 21:59:09,161 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 21:59:09,177 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 21:59:09,679 [INFO] ✅ 地址监控任务完成
2025-08-06 21:59:09,679 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:00:09,680 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:00:09,699 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:00:09,708 [INFO] 📋 监控地址数量: 1
2025-08-06 22:00:09,708 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:00:10,267 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:00:10.188+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:00:10,806 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:00:10,835 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:00:11,335 [INFO] ✅ 地址监控任务完成
2025-08-06 22:00:11,335 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:01:11,335 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:01:11,344 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:01:11,352 [INFO] 📋 监控地址数量: 1
2025-08-06 22:01:11,353 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:01:19,991 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:01:19.911+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:01:22,315 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:01:22,335 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:01:22,836 [INFO] ✅ 地址监控任务完成
2025-08-06 22:01:22,836 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:02:22,838 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:02:22,855 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:02:22,873 [INFO] 📋 监控地址数量: 1
2025-08-06 22:02:22,873 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:02:23,334 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:02:23.267+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:02:23,868 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:02:23,888 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:02:24,389 [INFO] ✅ 地址监控任务完成
2025-08-06 22:02:24,389 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:03:24,391 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:03:24,408 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:03:24,415 [INFO] 📋 监控地址数量: 1
2025-08-06 22:03:24,415 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:03:27,112 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:03:26.780+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:03:27,702 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:03:27,727 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:03:28,228 [INFO] ✅ 地址监控任务完成
2025-08-06 22:03:28,229 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:04:28,230 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:04:28,248 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:04:28,266 [INFO] 📋 监控地址数量: 1
2025-08-06 22:04:28,266 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:04:28,773 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:04:28.698+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:04:29,375 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:04:29,404 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:04:29,905 [INFO] ✅ 地址监控任务完成
2025-08-06 22:04:29,905 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:05:29,907 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:05:29,925 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:05:29,932 [INFO] 📋 监控地址数量: 1
2025-08-06 22:05:29,933 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:05:40,069 [ERROR] ❌ 获取USDT余额失败 TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA: HTTPSConnectionPool(host='api.trongrid.io', port=443): Read timed out. (read timeout=10)
2025-08-06 22:05:50,961 [WARNING] ⚠️ API密钥 1 查询失败: USDT=None, TRX=362.***********-08-06 22:05:50,961 [ERROR] ❌ 所有API密钥都查询失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 22:05:50,962 [WARNING] ⚠️ 获取地址余额失败: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-06 22:05:51,462 [INFO] ✅ 地址监控任务完成
2025-08-06 22:05:51,463 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:06:51,463 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:06:51,481 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:06:51,499 [INFO] 📋 监控地址数量: 1
2025-08-06 22:06:51,499 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:06:52,099 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:06:51.986+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:06:52,682 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:06:52,708 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:06:53,209 [INFO] ✅ 地址监控任务完成
2025-08-06 22:06:53,209 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:07:53,210 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:07:53,228 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:07:53,246 [INFO] 📋 监控地址数量: 1
2025-08-06 22:07:53,246 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:07:53,806 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:07:53.729+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:07:54,321 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:07:54,350 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:07:54,851 [INFO] ✅ 地址监控任务完成
2025-08-06 22:07:54,851 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:08:54,852 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:08:54,870 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:08:54,878 [INFO] 📋 监控地址数量: 1
2025-08-06 22:08:54,878 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:08:56,630 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:08:56.555+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:08:58,597 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:08:58,623 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:08:59,125 [INFO] ✅ 地址监控任务完成
2025-08-06 22:08:59,125 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:09:15,509 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 22:09:15,509 [INFO] ✅ 使用固定数据库配置
2025-08-06 22:09:15,514 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 22:09:15,514 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 22:09:15,514 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 22:09:15,514 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 22:09:15,514 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 22:09:15,514 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:09:15,531 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:09:15,538 [INFO] 📋 监控地址数量: 1
2025-08-06 22:09:15,538 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:09:15,655 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-06 22:09:15,656 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 22:09:16,093 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:09:15.999+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:09:16,633 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:09:16,649 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:09:17,150 [INFO] ✅ 地址监控任务完成
2025-08-06 22:09:17,150 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:09:59,126 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:09:59,143 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:09:59,151 [INFO] 📋 监控地址数量: 1
2025-08-06 22:09:59,152 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:09:59,669 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:09:59.595+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:10:01,330 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:10:01,359 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:10:01,860 [INFO] ✅ 地址监控任务完成
2025-08-06 22:10:01,861 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:11:01,862 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:11:01,879 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:11:01,887 [INFO] 📋 监控地址数量: 1
2025-08-06 22:11:01,887 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:11:02,437 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:11:02.364+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:11:03,001 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:11:03,026 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:11:03,528 [INFO] ✅ 地址监控任务完成
2025-08-06 22:11:03,528 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:12:03,530 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:12:03,548 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:12:03,556 [INFO] 📋 监控地址数量: 1
2025-08-06 22:12:03,556 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:12:04,354 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:12:04.164+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:12:05,239 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:12:05,265 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:12:05,767 [INFO] ✅ 地址监控任务完成
2025-08-06 22:12:05,767 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:13:05,767 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:13:05,787 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:13:05,805 [INFO] 📋 监控地址数量: 1
2025-08-06 22:13:05,806 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:13:06,752 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:13:06.680+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:13:07,445 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:13:07,468 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:13:07,969 [INFO] ✅ 地址监控任务完成
2025-08-06 22:13:07,969 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:14:07,970 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:14:07,987 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:14:08,004 [INFO] 📋 监控地址数量: 1
2025-08-06 22:14:08,004 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:14:08,663 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:14:08.563+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:14:09,201 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:14:09,226 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:14:09,727 [INFO] ✅ 地址监控任务完成
2025-08-06 22:14:09,727 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:15:09,728 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:15:09,736 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:15:09,753 [INFO] 📋 监控地址数量: 1
2025-08-06 22:15:09,753 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:15:12,505 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:15:12.424+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:15:15,001 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:15:15,026 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:15:15,527 [INFO] ✅ 地址监控任务完成
2025-08-06 22:15:15,527 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:16:09,422 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 22:16:09,422 [INFO] ✅ 使用固定数据库配置
2025-08-06 22:16:09,426 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 22:16:09,426 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 22:16:09,426 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 22:16:09,426 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 22:16:09,426 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 22:16:09,427 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:16:09,446 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:16:09,458 [INFO] 📋 监控地址数量: 1
2025-08-06 22:16:09,458 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:16:09,461 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 22:16:09,462 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 22:16:13,270 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:16:13,296 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:16:13,797 [INFO] ✅ 地址监控任务完成
2025-08-06 22:16:13,797 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:16:15,529 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:16:15,537 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:16:15,555 [INFO] 📋 监控地址数量: 1
2025-08-06 22:16:15,555 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:16:16,126 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:16:16.054+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:16:16,826 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:16:16,854 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:16:17,355 [INFO] ✅ 地址监控任务完成
2025-08-06 22:16:17,355 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:17:06,926 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 22:17:06,926 [INFO] ✅ 使用固定数据库配置
2025-08-06 22:17:06,929 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 22:17:06,930 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 22:17:06,930 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 22:17:06,930 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 22:17:06,930 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 22:17:06,930 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:17:06,947 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:17:06,965 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 22:17:06,965 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 22:17:06,972 [INFO] 📋 监控地址数量: 1
2025-08-06 22:17:06,972 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:17:08,473 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:17:08,496 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:17:08,997 [INFO] ✅ 地址监控任务完成
2025-08-06 22:17:08,997 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:17:17,355 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:17:17,363 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:17:17,371 [INFO] 📋 监控地址数量: 1
2025-08-06 22:17:17,372 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:17:18,044 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:17:17.954+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:17:18,691 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:17:18,722 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:17:19,223 [INFO] ✅ 地址监控任务完成
2025-08-06 22:17:19,223 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:18:08,997 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:18:09,004 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:18:09,012 [INFO] 📋 监控地址数量: 1
2025-08-06 22:18:09,012 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:18:10,186 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:18:10,202 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:18:10,702 [INFO] ✅ 地址监控任务完成
2025-08-06 22:18:10,702 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:18:19,224 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:18:19,232 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:18:19,249 [INFO] 📋 监控地址数量: 1
2025-08-06 22:18:19,249 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:18:20,283 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:18:19.766+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:18:20,906 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:18:20,925 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:18:21,427 [INFO] ✅ 地址监控任务完成
2025-08-06 22:18:21,428 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:19:10,703 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:19:10,712 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:19:10,720 [INFO] 📋 监控地址数量: 1
2025-08-06 22:19:10,720 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:19:11,927 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:19:11,961 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:19:12,461 [INFO] ✅ 地址监控任务完成
2025-08-06 22:19:12,461 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:19:21,429 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:19:21,446 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:19:21,465 [INFO] 📋 监控地址数量: 1
2025-08-06 22:19:21,465 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:19:23,055 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:19:22.987+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:19:23,733 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:19:23,752 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:19:24,253 [INFO] ✅ 地址监控任务完成
2025-08-06 22:19:24,253 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:20:12,462 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:20:12,470 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:20:12,487 [INFO] 📋 监控地址数量: 1
2025-08-06 22:20:12,487 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:20:15,323 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:20:15,338 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:20:15,838 [INFO] ✅ 地址监控任务完成
2025-08-06 22:20:15,838 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:20:24,254 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:20:24,266 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:20:24,283 [INFO] 📋 监控地址数量: 1
2025-08-06 22:20:24,283 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:20:25,711 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:20:25.613+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:20:26,312 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:20:26,339 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:20:26,840 [INFO] ✅ 地址监控任务完成
2025-08-06 22:20:26,840 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:21:15,839 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:21:15,847 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:21:15,865 [INFO] 📋 监控地址数量: 1
2025-08-06 22:21:15,865 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:21:18,546 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:21:18,576 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:21:19,078 [INFO] ✅ 地址监控任务完成
2025-08-06 22:21:19,078 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:21:26,841 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:21:26,850 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:21:26,866 [INFO] 📋 监控地址数量: 1
2025-08-06 22:21:26,867 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:21:28,531 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:21:28.476+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:21:29,078 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:21:29,099 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:21:29,600 [INFO] ✅ 地址监控任务完成
2025-08-06 22:21:29,600 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:22:19,078 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:22:19,098 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:22:19,117 [INFO] 📋 监控地址数量: 1
2025-08-06 22:22:19,117 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:22:28,145 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:22:28,169 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:22:28,669 [INFO] ✅ 地址监控任务完成
2025-08-06 22:22:28,669 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:22:29,600 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:22:29,617 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:22:29,635 [INFO] 📋 监控地址数量: 1
2025-08-06 22:22:29,636 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:22:30,298 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:22:30.235+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:22:31,400 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:22:31,429 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:22:31,930 [INFO] ✅ 地址监控任务完成
2025-08-06 22:22:31,931 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:23:28,670 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:23:28,678 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:23:28,687 [INFO] 📋 监控地址数量: 1
2025-08-06 22:23:28,687 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:23:30,035 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:23:30,058 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:23:30,560 [INFO] ✅ 地址监控任务完成
2025-08-06 22:23:30,560 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:23:31,932 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:23:31,951 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:23:31,970 [INFO] 📋 监控地址数量: 1
2025-08-06 22:23:31,970 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:23:32,632 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:23:32.563+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:23:33,642 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:23:33,663 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:23:34,164 [INFO] ✅ 地址监控任务完成
2025-08-06 22:23:34,164 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:24:30,560 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:24:30,579 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:24:30,595 [INFO] 📋 监控地址数量: 1
2025-08-06 22:24:30,595 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:24:32,199 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:24:32,221 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:24:32,721 [INFO] ✅ 地址监控任务完成
2025-08-06 22:24:32,721 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:24:34,165 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:24:34,174 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:24:34,192 [INFO] 📋 监控地址数量: 1
2025-08-06 22:24:34,192 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:24:35,210 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:24:34.720+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:24:36,868 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:24:36,890 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:24:37,391 [INFO] ✅ 地址监控任务完成
2025-08-06 22:24:37,392 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:25:32,721 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:25:32,741 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:25:32,758 [INFO] 📋 监控地址数量: 1
2025-08-06 22:25:32,758 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:25:35,693 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:25:35,718 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:25:36,218 [INFO] ✅ 地址监控任务完成
2025-08-06 22:25:36,218 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:25:37,393 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:25:37,411 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:25:37,427 [INFO] 📋 监控地址数量: 1
2025-08-06 22:25:37,428 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:25:38,118 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:25:38.039+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:25:38,770 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:25:38,798 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:25:39,299 [INFO] ✅ 地址监控任务完成
2025-08-06 22:25:39,299 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:26:36,219 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:26:36,238 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:26:36,255 [INFO] 📋 监控地址数量: 1
2025-08-06 22:26:36,256 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:26:38,352 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:26:38,380 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:26:38,881 [INFO] ✅ 地址监控任务完成
2025-08-06 22:26:38,881 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:26:39,301 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:26:39,318 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:26:39,336 [INFO] 📋 监控地址数量: 1
2025-08-06 22:26:39,337 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:26:40,387 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:26:40.317+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:26:41,043 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:26:41,071 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:26:41,571 [INFO] ✅ 地址监控任务完成
2025-08-06 22:26:41,571 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:27:38,882 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:27:38,890 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:27:38,906 [INFO] 📋 监控地址数量: 1
2025-08-06 22:27:38,906 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:27:40,282 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:27:40,308 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:27:40,809 [INFO] ✅ 地址监控任务完成
2025-08-06 22:27:40,809 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:27:41,573 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:27:41,590 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:27:41,606 [INFO] 📋 监控地址数量: 1
2025-08-06 22:27:41,607 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:27:42,199 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:27:42.107+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:27:44,395 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:27:44,423 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:27:44,924 [INFO] ✅ 地址监控任务完成
2025-08-06 22:27:44,924 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:28:40,809 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:28:40,825 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:28:40,833 [INFO] 📋 监控地址数量: 1
2025-08-06 22:28:40,833 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:28:44,791 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:28:44,806 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:28:44,926 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:28:44,944 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:28:44,962 [INFO] 📋 监控地址数量: 1
2025-08-06 22:28:44,962 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:28:45,307 [INFO] ✅ 地址监控任务完成
2025-08-06 22:28:45,307 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:28:45,919 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:28:45.865+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:28:46,974 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:28:47,003 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:28:47,504 [INFO] ✅ 地址监控任务完成
2025-08-06 22:28:47,505 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:29:45,308 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:29:45,325 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:29:45,341 [INFO] 📋 监控地址数量: 1
2025-08-06 22:29:45,342 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:29:47,166 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:29:47,184 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:29:47,506 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:29:47,515 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:29:47,522 [INFO] 📋 监控地址数量: 1
2025-08-06 22:29:47,523 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:29:47,685 [INFO] ✅ 地址监控任务完成
2025-08-06 22:29:47,685 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:29:48,027 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:29:47.969+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:29:49,646 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:29:49,665 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:29:50,166 [INFO] ✅ 地址监控任务完成
2025-08-06 22:29:50,166 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:30:47,686 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:30:47,695 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:30:47,703 [INFO] 📋 监控地址数量: 1
2025-08-06 22:30:47,704 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:30:49,106 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:30:49,129 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:30:49,629 [INFO] ✅ 地址监控任务完成
2025-08-06 22:30:49,629 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:30:50,168 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:30:50,186 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:30:50,193 [INFO] 📋 监控地址数量: 1
2025-08-06 22:30:50,194 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:30:50,748 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:30:50.679+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:30:51,279 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:30:51,307 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:30:51,808 [INFO] ✅ 地址监控任务完成
2025-08-06 22:30:51,808 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:31:49,630 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:31:49,647 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:31:49,665 [INFO] 📋 监控地址数量: 1
2025-08-06 22:31:49,665 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:31:51,022 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:31:51,047 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:31:51,548 [INFO] ✅ 地址监控任务完成
2025-08-06 22:31:51,548 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:31:51,809 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:31:51,827 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:31:51,835 [INFO] 📋 监控地址数量: 1
2025-08-06 22:31:51,836 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:31:52,408 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:31:52.347+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:31:55,179 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:31:55,208 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:31:55,709 [INFO] ✅ 地址监控任务完成
2025-08-06 22:31:55,709 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:32:51,548 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:32:51,558 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:32:51,568 [INFO] 📋 监控地址数量: 1
2025-08-06 22:32:51,568 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:32:55,017 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:32:55,030 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:32:55,531 [INFO] ✅ 地址监控任务完成
2025-08-06 22:32:55,531 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:32:55,711 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:32:55,728 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:32:55,736 [INFO] 📋 监控地址数量: 1
2025-08-06 22:32:55,736 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:32:57,028 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:32:56.303+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:32:57,730 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:32:57,749 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:32:58,251 [INFO] ✅ 地址监控任务完成
2025-08-06 22:32:58,251 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:33:55,531 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:33:55,547 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:33:55,557 [INFO] 📋 监控地址数量: 1
2025-08-06 22:33:55,557 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:33:58,058 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:33:58,072 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:33:58,251 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:33:58,268 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:33:58,284 [INFO] 📋 监控地址数量: 1
2025-08-06 22:33:58,284 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:33:58,573 [INFO] ✅ 地址监控任务完成
2025-08-06 22:33:58,573 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:33:59,279 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:33:59.229+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:34:01,087 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:34:01,109 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:34:01,610 [INFO] ✅ 地址监控任务完成
2025-08-06 22:34:01,610 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:34:58,573 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:34:58,591 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:34:58,599 [INFO] 📋 监控地址数量: 1
2025-08-06 22:34:58,599 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:35:00,378 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:35:00,402 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:35:00,902 [INFO] ✅ 地址监控任务完成
2025-08-06 22:35:00,902 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:35:01,611 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:35:01,619 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:35:01,636 [INFO] 📋 监控地址数量: 1
2025-08-06 22:35:01,637 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:35:02,561 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:35:02.510+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:35:04,651 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:35:04,680 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:35:05,181 [INFO] ✅ 地址监控任务完成
2025-08-06 22:35:05,181 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:36:00,904 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:36:00,920 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:36:00,937 [INFO] 📋 监控地址数量: 1
2025-08-06 22:36:00,937 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:36:02,185 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:36:02,212 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:36:02,714 [INFO] ✅ 地址监控任务完成
2025-08-06 22:36:02,714 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:36:05,182 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:36:05,200 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:36:05,218 [INFO] 📋 监控地址数量: 1
2025-08-06 22:36:05,219 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:36:05,936 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:36:05.851+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:36:06,515 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:36:06,540 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:36:07,041 [INFO] ✅ 地址监控任务完成
2025-08-06 22:36:07,041 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:37:02,714 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:37:02,722 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:37:02,738 [INFO] 📋 监控地址数量: 1
2025-08-06 22:37:02,738 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:37:04,071 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:37:04,093 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:37:04,594 [INFO] ✅ 地址监控任务完成
2025-08-06 22:37:04,594 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:37:07,043 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:37:07,051 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:37:07,069 [INFO] 📋 监控地址数量: 1
2025-08-06 22:37:07,069 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:37:08,623 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:37:08.563+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:37:10,823 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:37:10,842 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:37:11,343 [INFO] ✅ 地址监控任务完成
2025-08-06 22:37:11,343 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:38:04,595 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:38:04,612 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:38:04,620 [INFO] 📋 监控地址数量: 1
2025-08-06 22:38:04,620 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:38:05,794 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:38:05,809 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:38:06,310 [INFO] ✅ 地址监控任务完成
2025-08-06 22:38:06,310 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:38:11,345 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:38:11,362 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:38:11,370 [INFO] 📋 监控地址数量: 1
2025-08-06 22:38:11,370 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:38:13,046 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:38:12.981+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:38:13,619 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:38:13,649 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:38:14,150 [INFO] ✅ 地址监控任务完成
2025-08-06 22:38:14,150 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:39:06,311 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:39:06,327 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:39:06,344 [INFO] 📋 监控地址数量: 1
2025-08-06 22:39:06,344 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:39:08,080 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:39:08,095 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:39:08,596 [INFO] ✅ 地址监控任务完成
2025-08-06 22:39:08,596 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:39:14,151 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:39:14,159 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:39:14,167 [INFO] 📋 监控地址数量: 1
2025-08-06 22:39:14,168 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:39:14,745 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:39:14.685+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:39:15,757 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:39:15,783 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:39:16,284 [INFO] ✅ 地址监控任务完成
2025-08-06 22:39:16,284 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:40:08,596 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:40:08,614 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:40:08,630 [INFO] 📋 监控地址数量: 1
2025-08-06 22:40:08,630 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:40:10,271 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:40:10,297 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:40:10,797 [INFO] ✅ 地址监控任务完成
2025-08-06 22:40:10,797 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:40:16,286 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:40:16,294 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:40:16,302 [INFO] 📋 监控地址数量: 1
2025-08-06 22:40:16,303 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:40:17,347 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:40:16.878+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:40:18,266 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:40:18,293 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:40:18,794 [INFO] ✅ 地址监控任务完成
2025-08-06 22:40:18,794 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:41:10,797 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:41:10,806 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:41:10,824 [INFO] 📋 监控地址数量: 1
2025-08-06 22:41:10,824 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:41:13,195 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:41:13,212 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:41:13,713 [INFO] ✅ 地址监控任务完成
2025-08-06 22:41:13,713 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:41:18,795 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:41:18,813 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:41:18,821 [INFO] 📋 监控地址数量: 1
2025-08-06 22:41:18,822 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:41:19,386 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:41:19.329+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:41:19,984 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:41:20,011 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:41:20,512 [INFO] ✅ 地址监控任务完成
2025-08-06 22:41:20,513 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:42:13,714 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:42:13,726 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:42:13,737 [INFO] 📋 监控地址数量: 1
2025-08-06 22:42:13,737 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:42:17,361 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:42:17,375 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:42:17,875 [INFO] ✅ 地址监控任务完成
2025-08-06 22:42:17,875 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:42:20,514 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:42:20,532 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:42:20,548 [INFO] 📋 监控地址数量: 1
2025-08-06 22:42:20,549 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:42:21,074 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:42:21.027+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:42:21,762 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:42:21,779 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:42:22,280 [INFO] ✅ 地址监控任务完成
2025-08-06 22:42:22,280 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:43:17,876 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:43:17,883 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:43:17,901 [INFO] 📋 监控地址数量: 1
2025-08-06 22:43:17,901 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:43:20,931 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:43:20,950 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:43:21,451 [INFO] ✅ 地址监控任务完成
2025-08-06 22:43:21,451 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:43:22,290 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:43:22,318 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:43:22,338 [INFO] 📋 监控地址数量: 1
2025-08-06 22:43:22,340 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:43:23,012 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:43:22.945+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:43:23,721 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:43:23,760 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:43:24,261 [INFO] ✅ 地址监控任务完成
2025-08-06 22:43:24,262 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:44:21,451 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:44:21,467 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:44:21,479 [INFO] 📋 监控地址数量: 1
2025-08-06 22:44:21,479 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:44:24,262 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:44:24,280 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:44:24,298 [INFO] 📋 监控地址数量: 1
2025-08-06 22:44:24,299 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:44:26,481 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:44:26,506 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:44:26,769 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:44:25.983+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:44:27,006 [INFO] ✅ 地址监控任务完成
2025-08-06 22:44:27,006 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:44:27,392 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:44:27,420 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:44:27,921 [INFO] ✅ 地址监控任务完成
2025-08-06 22:44:27,921 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:45:27,007 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:45:27,025 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:45:27,032 [INFO] 📋 监控地址数量: 1
2025-08-06 22:45:27,033 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:45:27,923 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:45:27,931 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:45:27,947 [INFO] 📋 监控地址数量: 1
2025-08-06 22:45:27,947 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:45:28,528 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:45:28.473+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:45:29,432 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:45:29,458 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:45:29,959 [INFO] ✅ 地址监控任务完成
2025-08-06 22:45:29,959 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:45:31,452 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:45:31,481 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:45:31,982 [INFO] ✅ 地址监控任务完成
2025-08-06 22:45:31,982 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:46:29,959 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:46:29,968 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:46:29,986 [INFO] 📋 监控地址数量: 1
2025-08-06 22:46:29,987 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:46:31,983 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:46:31,993 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:46:32,011 [INFO] 📋 监控地址数量: 1
2025-08-06 22:46:32,012 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:46:32,565 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:46:32.510+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:46:32,717 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:46:32,733 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:46:33,233 [INFO] ✅ 地址监控任务完成
2025-08-06 22:46:33,233 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:46:35,042 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:46:35,072 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:46:35,573 [INFO] ✅ 地址监控任务完成
2025-08-06 22:46:35,574 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:47:33,236 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:47:33,253 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:47:33,263 [INFO] 📋 监控地址数量: 1
2025-08-06 22:47:33,264 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:47:35,575 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:47:35,593 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:47:35,600 [INFO] 📋 监控地址数量: 1
2025-08-06 22:47:35,600 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:47:35,777 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:47:35,805 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:47:36,224 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:47:36.139+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:47:36,307 [INFO] ✅ 地址监控任务完成
2025-08-06 22:47:36,307 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:47:37,185 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:47:37,204 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:47:37,706 [INFO] ✅ 地址监控任务完成
2025-08-06 22:47:37,707 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:48:36,307 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:48:36,324 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:48:36,341 [INFO] 📋 监控地址数量: 1
2025-08-06 22:48:36,342 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:48:37,665 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:48:37,694 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:48:37,707 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:48:37,725 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:48:37,743 [INFO] 📋 监控地址数量: 1
2025-08-06 22:48:37,744 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:48:38,195 [INFO] ✅ 地址监控任务完成
2025-08-06 22:48:38,195 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:48:38,870 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:48:38.833+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:48:39,831 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:48:39,856 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:48:40,358 [INFO] ✅ 地址监控任务完成
2025-08-06 22:48:40,358 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:49:38,195 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:49:38,212 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:49:38,228 [INFO] 📋 监控地址数量: 1
2025-08-06 22:49:38,228 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:49:39,462 [INFO] ✅ 查询成功: USDT=3.62, TRX=362.***********-08-06 22:49:39,487 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 362.***********-08-06 22:49:39,988 [INFO] ✅ 地址监控任务完成
2025-08-06 22:49:39,988 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:49:40,359 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:49:40,368 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:49:40,376 [INFO] 📋 监控地址数量: 1
2025-08-06 22:49:40,377 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:49:41,292 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:49:41.238+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:49:43,365 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:49:43,397 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 0.0, TRX: 362.***********-08-06 22:49:43,899 [INFO] ✅ 地址监控任务完成
2025-08-06 22:49:43,899 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:50:43,901 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:50:43,919 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:50:43,935 [INFO] 📋 监控地址数量: 1
2025-08-06 22:50:43,936 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:50:45,553 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:50:45.524+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:50:47,021 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:50:47,046 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:50:47,547 [INFO] ✅ 地址监控任务完成
2025-08-06 22:50:47,547 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:51:47,548 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:51:47,557 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:51:47,565 [INFO] 📋 监控地址数量: 1
2025-08-06 22:51:47,565 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:51:48,118 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:51:48.092+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:51:50,066 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:51:50,085 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:51:50,585 [INFO] ✅ 地址监控任务完成
2025-08-06 22:51:50,585 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:52:50,587 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:52:50,605 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:52:50,623 [INFO] 📋 监控地址数量: 1
2025-08-06 22:52:50,623 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:52:51,197 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:52:51.158+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:52:51,863 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:52:51,887 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:52:52,388 [INFO] ✅ 地址监控任务完成
2025-08-06 22:52:52,388 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:53:52,388 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:53:52,405 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:53:52,412 [INFO] 📋 监控地址数量: 1
2025-08-06 22:53:52,413 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:53:53,397 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:53:53.356+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:53:54,152 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:53:54,170 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:53:54,671 [INFO] ✅ 地址监控任务完成
2025-08-06 22:53:54,671 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:54:54,672 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:54:54,680 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:54:54,697 [INFO] 📋 监控地址数量: 1
2025-08-06 22:54:54,697 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:54:55,333 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:54:55.303+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:54:55,926 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:54:55,942 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:54:56,443 [INFO] ✅ 地址监控任务完成
2025-08-06 22:54:56,443 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:55:56,445 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:55:56,455 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:55:56,473 [INFO] 📋 监控地址数量: 1
2025-08-06 22:55:56,474 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:55:59,273 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:55:59.205+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:55:59,972 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:56:00,002 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:56:00,502 [INFO] ✅ 地址监控任务完成
2025-08-06 22:56:00,502 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:57:00,504 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:57:00,522 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:57:00,540 [INFO] 📋 监控地址数量: 1
2025-08-06 22:57:00,540 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:57:01,300 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:57:01.140+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:57:02,276 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:57:02,301 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:57:02,802 [INFO] ✅ 地址监控任务完成
2025-08-06 22:57:02,802 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:58:02,803 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:58:02,812 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:58:02,830 [INFO] 📋 监控地址数量: 1
2025-08-06 22:58:02,830 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:58:03,841 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:58:03.388+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:58:05,011 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:58:05,034 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:58:05,535 [INFO] ✅ 地址监控任务完成
2025-08-06 22:58:05,535 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 22:59:05,535 [INFO] 🔍 开始执行地址监控任务
2025-08-06 22:59:05,544 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 22:59:05,561 [INFO] 📋 监控地址数量: 1
2025-08-06 22:59:05,561 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 22:59:07,788 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T14:59:07.755+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 22:59:09,137 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 22:59:09,162 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 22:59:09,664 [INFO] ✅ 地址监控任务完成
2025-08-06 22:59:09,664 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:00:09,665 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:00:09,684 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:00:09,692 [INFO] 📋 监控地址数量: 1
2025-08-06 23:00:09,693 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:00:10,667 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:00:10.529+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:00:11,684 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:00:11,703 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:00:12,203 [INFO] ✅ 地址监控任务完成
2025-08-06 23:00:12,203 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:01:12,205 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:01:12,222 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:01:12,239 [INFO] 📋 监控地址数量: 1
2025-08-06 23:01:12,240 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:01:13,317 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:01:13.274+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:01:13,964 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:01:13,976 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:01:14,478 [INFO] ✅ 地址监控任务完成
2025-08-06 23:01:14,479 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:02:14,479 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:02:14,498 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:02:14,507 [INFO] 📋 监控地址数量: 1
2025-08-06 23:02:14,507 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:02:15,145 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:02:15.028+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:02:16,894 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:02:16,913 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:02:17,415 [INFO] ✅ 地址监控任务完成
2025-08-06 23:02:17,416 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:03:17,416 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:03:17,433 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:03:17,441 [INFO] 📋 监控地址数量: 1
2025-08-06 23:03:17,442 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:03:18,014 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:03:17.963+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:03:20,106 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:03:20,131 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:03:20,632 [INFO] ✅ 地址监控任务完成
2025-08-06 23:03:20,632 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:04:20,633 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:04:20,642 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:04:20,659 [INFO] 📋 监控地址数量: 1
2025-08-06 23:04:20,660 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:04:22,563 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:04:22.139+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:04:23,236 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:04:23,254 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:04:23,754 [INFO] ✅ 地址监控任务完成
2025-08-06 23:04:23,755 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:05:23,756 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:05:23,764 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:05:23,782 [INFO] 📋 监控地址数量: 1
2025-08-06 23:05:23,782 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:05:24,309 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:05:24.280+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:05:24,903 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:05:24,920 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:05:25,421 [INFO] ✅ 地址监控任务完成
2025-08-06 23:05:25,421 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:06:25,421 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:06:25,439 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:06:25,458 [INFO] 📋 监控地址数量: 1
2025-08-06 23:06:25,458 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:06:26,520 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:06:26.475+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:06:28,381 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:06:28,399 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:06:28,901 [INFO] ✅ 地址监控任务完成
2025-08-06 23:06:28,901 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:07:28,902 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:07:28,919 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:07:28,937 [INFO] 📋 监控地址数量: 1
2025-08-06 23:07:28,937 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:07:29,556 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:07:29.520+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:07:30,276 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:07:30,291 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:07:30,792 [INFO] ✅ 地址监控任务完成
2025-08-06 23:07:30,792 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:08:30,794 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:08:30,813 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:08:30,821 [INFO] 📋 监控地址数量: 1
2025-08-06 23:08:30,821 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:08:31,367 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:08:31.352+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:08:31,942 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:08:31,964 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:08:32,465 [INFO] ✅ 地址监控任务完成
2025-08-06 23:08:32,466 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:09:32,467 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:09:32,475 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:09:32,484 [INFO] 📋 监控地址数量: 1
2025-08-06 23:09:32,484 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:09:33,119 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:09:33.092+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:09:34,138 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:09:34,153 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:09:34,653 [INFO] ✅ 地址监控任务完成
2025-08-06 23:09:34,653 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:10:34,655 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:10:34,663 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:10:34,671 [INFO] 📋 监控地址数量: 1
2025-08-06 23:10:34,671 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:10:38,719 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:10:38.687+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:10:40,798 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:10:40,822 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:10:41,323 [INFO] ✅ 地址监控任务完成
2025-08-06 23:10:41,324 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:11:41,325 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:11:41,343 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:11:41,360 [INFO] 📋 监控地址数量: 1
2025-08-06 23:11:41,361 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:11:41,957 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:11:41.932+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:11:43,629 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:11:43,646 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:11:44,148 [INFO] ✅ 地址监控任务完成
2025-08-06 23:11:44,148 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:12:44,150 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:12:44,166 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:12:44,181 [INFO] 📋 监控地址数量: 1
2025-08-06 23:12:44,182 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:12:44,740 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:12:44.717+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:12:46,964 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:12:46,990 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:12:47,491 [INFO] ✅ 地址监控任务完成
2025-08-06 23:12:47,491 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:13:47,492 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:13:47,501 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:13:47,517 [INFO] 📋 监控地址数量: 1
2025-08-06 23:13:47,518 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:13:48,409 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:13:48.399+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:13:49,032 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:13:49,050 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:13:49,550 [INFO] ✅ 地址监控任务完成
2025-08-06 23:13:49,550 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:14:49,550 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:14:49,559 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:14:49,578 [INFO] 📋 监控地址数量: 1
2025-08-06 23:14:49,579 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:14:50,263 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:14:50.226+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:14:51,693 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:14:51,712 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:14:52,214 [INFO] ✅ 地址监控任务完成
2025-08-06 23:14:52,214 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:15:52,215 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:15:52,232 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:15:52,250 [INFO] 📋 监控地址数量: 1
2025-08-06 23:15:52,251 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:16:03,191 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:16:03.140+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:16:04,173 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:16:04,197 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:16:04,699 [INFO] ✅ 地址监控任务完成
2025-08-06 23:16:04,699 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:17:04,701 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:17:04,720 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:17:04,736 [INFO] 📋 监控地址数量: 1
2025-08-06 23:17:04,737 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:17:05,324 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:17:05.297+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:17:06,203 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:17:06,231 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:17:06,732 [INFO] ✅ 地址监控任务完成
2025-08-06 23:17:06,732 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:18:06,734 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:18:06,751 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:18:06,768 [INFO] 📋 监控地址数量: 1
2025-08-06 23:18:06,769 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:18:07,841 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:18:07.414+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:18:08,827 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:18:08,845 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:18:09,345 [INFO] ✅ 地址监控任务完成
2025-08-06 23:18:09,345 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:19:09,347 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:19:09,358 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:19:09,367 [INFO] 📋 监控地址数量: 1
2025-08-06 23:19:09,368 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:19:10,331 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:19:10.313+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:19:11,103 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:19:11,127 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:19:11,628 [INFO] ✅ 地址监控任务完成
2025-08-06 23:19:11,628 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:20:11,628 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:20:11,646 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:20:11,663 [INFO] 📋 监控地址数量: 1
2025-08-06 23:20:11,663 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:20:12,326 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:20:12.305+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:20:13,028 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:20:13,045 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:20:13,546 [INFO] ✅ 地址监控任务完成
2025-08-06 23:20:13,546 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:21:13,548 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:21:13,556 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:21:13,564 [INFO] 📋 监控地址数量: 1
2025-08-06 23:21:13,565 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:21:14,292 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:21:14.260+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:21:15,657 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:21:15,688 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:21:16,189 [INFO] ✅ 地址监控任务完成
2025-08-06 23:21:16,189 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:22:16,190 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:22:16,208 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:22:16,215 [INFO] 📋 监控地址数量: 1
2025-08-06 23:22:16,216 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:22:18,195 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:22:18.155+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:22:19,910 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:22:19,926 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:22:20,428 [INFO] ✅ 地址监控任务完成
2025-08-06 23:22:20,428 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:23:20,429 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:23:20,451 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:23:20,473 [INFO] 📋 监控地址数量: 1
2025-08-06 23:23:20,473 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:23:22,078 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:23:22.061+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:23:23,664 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:23:23,708 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:23:24,210 [INFO] ✅ 地址监控任务完成
2025-08-06 23:23:24,210 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:24:24,210 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:24:24,230 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:24:24,249 [INFO] 📋 监控地址数量: 1
2025-08-06 23:24:24,249 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:24:26,179 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:24:26.166+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:24:26,757 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:24:26,777 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:24:27,279 [INFO] ✅ 地址监控任务完成
2025-08-06 23:24:27,279 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:25:27,280 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:25:27,297 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:25:27,304 [INFO] 📋 监控地址数量: 1
2025-08-06 23:25:27,304 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:25:28,282 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:25:27.877+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:25:29,894 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:25:29,913 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:25:30,415 [INFO] ✅ 地址监控任务完成
2025-08-06 23:25:30,415 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:26:30,416 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:26:30,433 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:26:30,450 [INFO] 📋 监控地址数量: 1
2025-08-06 23:26:30,451 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:26:31,150 [WARNING] ⚠️ USDT查询HTTP错误: 404 - {"timestamp":"2025-08-06T15:26:31.050+00:00","status":404,"error":"Not Found","path":"/v1/accounts/TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA/trc20"}
2025-08-06 23:26:32,253 [INFO] ✅ 查询成功: USDT=0.0, TRX=362.***********-08-06 23:26:32,268 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 0.0, TRX: 362.***********-08-06 23:26:32,769 [INFO] ✅ 地址监控任务完成
2025-08-06 23:26:32,769 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-06 23:58:09,701 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-06 23:58:09,702 [INFO] ✅ 使用固定数据库配置
2025-08-06 23:58:09,706 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-06 23:58:09,707 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-06 23:58:09,707 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-06 23:58:09,707 [INFO] 🚀 USDT授权地址监控器启动
2025-08-06 23:58:09,707 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-06 23:58:09,707 [INFO] 🔍 开始执行地址监控任务
2025-08-06 23:58:09,731 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-06 23:58:09,747 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-06 23:58:09,748 [INFO] [33mPress CTRL+C to quit[0m
2025-08-06 23:58:09,754 [INFO] 📋 监控地址数量: 1
2025-08-06 23:58:09,754 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-06 23:58:11,653 [INFO] ✅ 查询成功: USDT=3.62, TRX=360.04557
2025-08-06 23:58:11,687 [INFO] 📊 TQ9UULobt2... USDT: 0.000000 -> 3.62, TRX: 360.04557
2025-08-06 23:58:12,188 [INFO] ✅ 地址监控任务完成
2025-08-06 23:58:12,188 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 00:10:49,508 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 00:10:49,509 [INFO] ✅ 使用固定数据库配置
2025-08-07 00:10:49,513 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 00:10:49,513 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 00:10:49,513 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 00:10:49,514 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 00:10:49,514 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 00:10:49,514 [INFO] 🔍 开始执行地址监控任务
2025-08-07 00:10:49,533 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 00:10:49,533 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 00:10:49,551 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 00:10:49,551 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 00:10:49,560 [ERROR] ❌ fish表数据完整性检查失败: (1267, "Illegal mix of collations (utf8mb4_unicode_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='")
2025-08-07 00:10:49,567 [INFO] 📋 监控地址数量: 1
2025-08-07 00:10:49,568 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-07 00:10:52,383 [INFO] ✅ 查询成功: USDT=3.62, TRX=360.04557
2025-08-07 00:10:52,400 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 3.62, TRX: 360.04557
2025-08-07 00:10:52,431 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-07 00:14:43,877 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 00:14:43,877 [INFO] ✅ 使用固定数据库配置
2025-08-07 00:14:43,880 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 00:14:43,880 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 00:14:43,881 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 00:14:43,881 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 00:14:43,881 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 00:14:43,881 [INFO] 🔍 开始执行地址监控任务
2025-08-07 00:14:43,899 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 00:14:43,899 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 00:14:43,916 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 00:14:43,916 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 00:14:43,928 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 00:14:43,935 [INFO] 📋 监控地址数量: 1
2025-08-07 00:14:43,936 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-07 00:14:45,381 [INFO] ✅ 查询成功: USDT=3.62, TRX=360.04557
2025-08-07 00:14:45,405 [INFO] 📊 TQ9UULobt2... USDT: 3.620000 -> 3.62, TRX: 360.04557
2025-08-07 00:14:45,418 [INFO] 🔍 开始查询地址余额: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, API密钥数量: 1
2025-08-07 00:14:47,674 [INFO] ✅ 查询成功: USDT=3.62, TRX=360.04557
2025-08-07 00:14:47,674 [INFO] 💰 获取最新余额成功: USDT=3.62, TRX=360.04557
2025-08-07 00:14:47,679 [INFO] 📊 更新fish表记录: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 00:14:47,687 [INFO] ✅ 数据已同步到fish表: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, USDT=3.62, TRX=360.04557
2025-08-07 00:14:48,195 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 00:14:48,195 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:37:21,641 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 05:37:21,643 [INFO] ✅ 使用固定数据库配置
2025-08-07 05:37:21,647 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 05:37:21,647 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 05:37:21,647 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 05:37:21,647 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 05:37:21,647 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 05:37:21,648 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:37:21,667 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:37:21,668 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:37:21,687 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:37:21,689 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 05:37:21,689 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 05:37:21,695 [INFO] 📭 没有需要监控的地址
2025-08-07 05:37:21,695 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:38:21,696 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:38:21,714 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:38:21,714 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:38:21,735 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:38:21,754 [INFO] 📭 没有需要监控的地址
2025-08-07 05:38:21,754 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:39:21,755 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:39:21,773 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:39:21,774 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:39:21,786 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:39:21,805 [INFO] 📭 没有需要监控的地址
2025-08-07 05:39:21,805 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:40:21,805 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:40:21,814 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:40:21,814 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:40:21,823 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:40:21,842 [INFO] 📭 没有需要监控的地址
2025-08-07 05:40:21,843 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:41:21,844 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:41:21,852 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:41:21,853 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:41:21,870 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:41:21,878 [INFO] 📭 没有需要监控的地址
2025-08-07 05:41:21,878 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:42:21,878 [INFO] 🔍 开始执行地址监控任务
2025-08-07 05:42:21,887 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 05:42:21,887 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 05:42:21,905 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 05:42:21,925 [INFO] 📋 监控地址数量: 1
2025-08-07 05:42:21,925 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 05:42:23,147 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.524579
2025-08-07 05:42:23,160 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 12.52, TRX: 53.524579
2025-08-07 05:42:23,174 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 05:42:24,194 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.524579
2025-08-07 05:42:24,194 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=53.524579
2025-08-07 05:42:24,200 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 05:42:24,208 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=53.524579
2025-08-07 05:42:24,209 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 阈值: 10.000000
2025-08-07 05:42:24,225 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 金额: 12.52 USDT
2025-08-07 05:42:24,225 [INFO] 📋 使用权限地址: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 05:42:24,225 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 05:42:24,226 [WARNING] ⚠️ 地址格式异常，尝试直接使用: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 05:42:24,226 [WARNING] ⚠️ 地址格式异常，尝试直接使用: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 05:42:24,576 [ERROR] ❌ 交易创建失败: {'result': {'code': 'OTHER_ERROR', 'message': 'class org.bouncycastle.util.encoders.DecoderException : exception decoding Hex string: invalid characters encountered in Hex string'}}
2025-08-07 05:42:24,581 [ERROR] ❌ 转账失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 05:42:25,100 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 05:42:25,100 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 05:47:15,974 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 05:47:15,975 [INFO] ✅ 使用固定数据库配置
2025-08-07 05:47:15,979 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 05:47:15,980 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 05:47:15,980 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 05:47:15,980 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 05:47:16,021 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 05:47:16,023 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 05:47:16,371 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:16,371 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:16,372 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 05:47:16,701 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:16,701 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:16,702 [WARNING] ⚠️ 使用简化转换: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t -> fb2e4998929bc2df4bf6a158d887c5040987f3d7
2025-08-07 05:47:17,039 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,040 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,040 [WARNING] ⚠️ 使用简化转换: TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7 -> ec9594401fc395b0a3446a024fa789258ad3dfd6
2025-08-07 05:47:17,401 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,401 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,402 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 05:47:17,723 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,724 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:17,725 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 05:47:17,731 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 05:47:17,732 [INFO] ✅ 使用固定数据库配置
2025-08-07 05:47:17,735 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 05:47:17,735 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 05:47:17,735 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 05:47:17,736 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 05:47:17,748 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 05:47:17,749 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 05:47:18,065 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 05:47:18,066 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 06:12:36,512 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 06:12:36,512 [INFO] ✅ 使用固定数据库配置
2025-08-07 06:12:36,516 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 06:12:36,516 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 06:12:36,516 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 06:12:36,517 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 06:12:36,553 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 06:12:36,554 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 06:27:09,716 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 06:27:09,716 [INFO] ✅ 使用固定数据库配置
2025-08-07 06:27:09,721 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 06:27:09,722 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 06:27:09,722 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 06:27:09,722 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 06:27:09,722 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 06:27:09,723 [INFO] 🔍 开始执行地址监控任务
2025-08-07 06:27:09,740 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 06:27:09,740 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 06:27:09,762 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 06:27:09,762 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 06:27:09,763 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 06:27:09,780 [INFO] 📋 监控地址数量: 1
2025-08-07 06:27:09,788 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:27:10,826 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.52458
2025-08-07 06:27:10,843 [INFO] 📊 TJp1dGHZ1S... USDT: 12.520000 -> 12.52, TRX: 53.52458
2025-08-07 06:27:10,867 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:27:11,907 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.52458
2025-08-07 06:27:11,908 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=53.52458
2025-08-07 06:27:11,912 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:27:11,918 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=53.52458
2025-08-07 06:27:12,437 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 06:27:12,438 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 06:28:12,438 [INFO] 🔍 开始执行地址监控任务
2025-08-07 06:28:12,454 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 06:28:12,455 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 06:28:12,473 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 06:28:12,492 [INFO] 📋 监控地址数量: 1
2025-08-07 06:28:12,509 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:28:13,561 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.52458
2025-08-07 06:28:13,586 [INFO] 📊 TJp1dGHZ1S... USDT: 12.520000 -> 12.52, TRX: 53.52458
2025-08-07 06:28:13,620 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:28:14,779 [INFO] ✅ 查询成功: USDT=12.52, TRX=53.52458
2025-08-07 06:28:14,780 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=53.52458
2025-08-07 06:28:14,785 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:28:14,793 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=53.52458
2025-08-07 06:28:14,793 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 全局阈值: 10
2025-08-07 06:28:14,811 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 金额: 12.52 USDT
2025-08-07 06:28:14,811 [INFO] 📋 使用权限地址: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:28:14,811 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 06:28:15,173 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 06:28:15,173 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 06:28:15,173 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 06:28:15,516 [WARNING] ⚠️ API转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 06:28:15,516 [ERROR] ❌ 地址转换失败: API返回格式异常: {'result': True, 'message': 'Base58check format'}
2025-08-07 06:28:15,517 [WARNING] ⚠️ 使用简化转换: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> be9f3496df7c8dd3802c76fb04d5014a906fa148
2025-08-07 06:28:15,874 [ERROR] ❌ 缺少签名依赖库，请安装: pip install ecdsa
2025-08-07 06:28:15,874 [INFO] 💡 或者使用简化签名模式（仅用于测试）
2025-08-07 06:28:15,874 [WARNING] ⚠️ 使用模拟签名，仅用于测试！
2025-08-07 06:28:16,207 [ERROR] ❌ 交易广播失败: {'code': 'SIGERROR', 'txid': '4fe5a4e7a33fc74a5b1d5dd6753ec5ca1af05ce9b04ce875bd7933f5a7c55d30', 'message': 'Validate signature error: Signature size is 33'}
2025-08-07 06:28:16,216 [ERROR] ❌ 转账失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:28:16,724 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 06:28:16,724 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 06:36:47,937 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 06:36:47,938 [INFO] ✅ 使用固定数据库配置
2025-08-07 06:36:47,942 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 06:36:47,942 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 06:36:47,943 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 06:36:47,943 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 06:36:47,943 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 06:36:47,943 [INFO] 🔍 开始执行地址监控任务
2025-08-07 06:36:47,962 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 06:36:47,962 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 06:36:47,983 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 06:36:47,983 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 06:36:47,983 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 06:36:48,002 [INFO] 📋 监控地址数量: 1
2025-08-07 06:36:48,010 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:36:49,074 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:36:49,101 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 12.52, TRX: 50.67958
2025-08-07 06:36:49,132 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:36:50,214 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:36:50,214 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=50.67958
2025-08-07 06:36:50,218 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:36:50,224 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=50.67958
2025-08-07 06:36:50,224 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 全局阈值: 10
2025-08-07 06:36:50,241 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, 金额: 12.52 USDT
2025-08-07 06:36:50,241 [INFO] 📋 使用权限地址: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 06:36:50,241 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 06:36:51,305 [ERROR] ❌ 缺少签名依赖库，请安装: pip install ecdsa
2025-08-07 06:36:51,305 [INFO] 💡 或者使用简化签名模式（仅用于测试）
2025-08-07 06:36:51,306 [WARNING] ⚠️ 使用模拟签名，仅用于测试！
2025-08-07 06:36:51,639 [ERROR] ❌ 交易广播失败: {'code': 'SIGERROR', 'txid': 'c84549f094db720b15a76a54c7c47dc48d07edbf3acd8242c2142ecc9f8893e4', 'message': 'Validate signature error: Signature size is 33'}
2025-08-07 06:36:51,649 [ERROR] ❌ 转账失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:36:52,166 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 06:36:52,166 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 06:39:53,535 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 06:39:53,535 [INFO] ✅ 使用固定数据库配置
2025-08-07 06:39:53,539 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 06:39:53,541 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 06:39:53,541 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 06:39:53,541 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 06:39:53,541 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 06:39:53,541 [INFO] 🔍 开始执行地址监控任务
2025-08-07 06:39:53,562 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 06:39:53,563 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 06:39:53,580 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 06:39:53,580 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 06:39:53,586 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 06:39:53,601 [INFO] 📋 监控地址数量: 1
2025-08-07 06:39:53,619 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:39:54,748 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:39:54,764 [INFO] 📊 TJp1dGHZ1S... USDT: 12.520000 -> 12.52, TRX: 50.67958
2025-08-07 06:39:54,802 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:39:55,816 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:39:55,816 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=50.67958
2025-08-07 06:39:55,820 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:39:55,827 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=50.67958
2025-08-07 06:39:55,828 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 全局阈值: 10
2025-08-07 06:39:55,835 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, 金额: 12.52 USDT
2025-08-07 06:39:55,835 [INFO] 📋 使用权限地址: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 06:39:55,835 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 06:39:57,288 [ERROR] ❌ 交易广播失败: {'code': 'SIGERROR', 'txid': '3b8f152580ac022ff3b8da5f9f005c038d0109fd43bf81ef7cf79fe8e552d2d9', 'message': 'Validate signature error: 62ceca4045f70005647e9ad76dc2eee766d71319142da364d09acbe7e825d89cbf2e1eefbec1c496c5df147740e565804b1a5deee14f37bbee72fb4727c76e1800 is signed by TDp1NzcNTHtxmevVSCPVjfFwksvKDp4WGc but it is not contained of permission.'}
2025-08-07 06:39:57,297 [ERROR] ❌ 转账失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:39:57,815 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 06:39:57,815 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 06:58:04,601 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 06:58:04,601 [INFO] ✅ 使用固定数据库配置
2025-08-07 06:58:04,605 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 06:58:04,605 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 06:58:04,606 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 06:58:04,606 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 06:58:04,606 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 06:58:04,606 [INFO] 🔍 开始执行地址监控任务
2025-08-07 06:58:04,625 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 06:58:04,625 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 06:58:04,645 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 06:58:04,646 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 06:58:04,655 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 06:58:04,672 [INFO] 📋 监控地址数量: 1
2025-08-07 06:58:04,689 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:58:05,822 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:58:05,849 [INFO] 📊 TJp1dGHZ1S... USDT: 12.520000 -> 12.52, TRX: 50.67958
2025-08-07 06:58:05,883 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 06:58:06,929 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 06:58:06,929 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=50.67958
2025-08-07 06:58:06,934 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:58:06,943 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=50.67958
2025-08-07 06:58:06,943 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 全局阈值: 10
2025-08-07 06:58:06,951 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, 金额: 12.52 USDT
2025-08-07 06:58:06,951 [INFO] 📋 使用权限地址: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 06:58:06,951 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 06:58:08,070 [ERROR] ❌ TronPy转账异常: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /wallet/getcontract (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))
2025-08-07 06:58:08,081 [ERROR] 详细错误: Traceback (most recent call last):
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connectionpool.py", line 773, in urlopen
    self._prepare_proxy(conn)
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connectionpool.py", line 1042, in _prepare_proxy
    conn.connect()
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connection.py", line 762, in connect
    self.sock = sock = self._connect_tls_proxy(self.host, sock)
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connection.py", line 862, in _connect_tls_proxy
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connection.py", line 969, in _ssl_wrap_socket_and_match_hostname
    ssl_sock = ssl_wrap_socket(
  File "D:\Program Files\Python39\lib\site-packages\urllib3\util\ssl_.py", line 480, in ssl_wrap_socket
    ssl_sock = _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)
  File "D:\Program Files\Python39\lib\site-packages\urllib3\util\ssl_.py", line 524, in _ssl_wrap_socket_impl
    return ssl_context.wrap_socket(sock, server_hostname=server_hostname)
  File "D:\Program Files\Python39\lib\ssl.py", line 500, in wrap_socket
    return self.sslsocket_class._create(
  File "D:\Program Files\Python39\lib\ssl.py", line 1040, in _create
    self.do_handshake()
  File "D:\Program Files\Python39\lib\ssl.py", line 1309, in do_handshake
    self._sslobj.do_handshake()
FileNotFoundError: [Errno 2] No such file or directory

The above exception was the direct cause of the following exception:

urllib3.exceptions.ProxyError: ('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory'))

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Program Files\Python39\lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
  File "D:\Program Files\Python39\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "D:\Program Files\Python39\lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /wallet/getcontract (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\py-ide\dao_rjCFJ\dujiaoka\dingshijiance.py", line 468, in tronpy_transfer_from
    contract = client.get_contract(usdt_contract)
  File "D:\Program Files\Python39\lib\site-packages\tronpy\tron.py", line 1030, in get_contract
    info = self.provider.make_request("wallet/getcontract", {"value": addr, "visible": True})
  File "D:\Program Files\Python39\lib\site-packages\tronpy\providers\http.py", line 82, in make_request
    resp = self.sess.post(url, json=params, timeout=self.timeout)
  File "D:\Program Files\Python39\lib\site-packages\requests\sessions.py", line 637, in post
    return self.request("POST", url, data=data, json=json, **kwargs)
  File "D:\Program Files\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "D:\Program Files\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "D:\Program Files\Python39\lib\site-packages\requests\adapters.py", line 694, in send
    raise ProxyError(e, request=request)
requests.exceptions.ProxyError: HTTPSConnectionPool(host='api.trongrid.io', port=443): Max retries exceeded with url: /wallet/getcontract (Caused by ProxyError('Unable to connect to proxy', FileNotFoundError(2, 'No such file or directory')))

2025-08-07 06:58:08,082 [ERROR] ❌ 转账失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 06:58:08,599 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 06:58:08,599 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 07:10:06,791 [INFO] 🌐 强制直连模式，禁用所有代理
2025-08-07 07:10:06,792 [INFO] ✅ 使用固定数据库配置
2025-08-07 07:10:06,796 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 07:10:06,796 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 07:10:06,796 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 07:10:06,797 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 07:10:06,797 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 07:10:06,797 [INFO] 🔍 开始执行地址监控任务
2025-08-07 07:10:06,812 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 07:10:06,812 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 07:10:06,827 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 07:10:06,828 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 07:10:06,837 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 07:10:06,853 [INFO] 📋 监控地址数量: 1
2025-08-07 07:10:06,868 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:10:07,918 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 07:10:07,941 [INFO] 📊 TJp1dGHZ1S... USDT: 12.520000 -> 12.52, TRX: 50.67958
2025-08-07 07:10:07,963 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:10:08,950 [INFO] ✅ 查询成功: USDT=12.52, TRX=50.67958
2025-08-07 07:10:08,950 [INFO] 💰 获取最新余额成功: USDT=12.52, TRX=50.67958
2025-08-07 07:10:08,954 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 07:10:08,960 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=12.52, TRX=50.67958
2025-08-07 07:10:08,960 [INFO] 💰 需要转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 余额: 12.52, 全局阈值: 10
2025-08-07 07:10:08,967 [INFO] 🔄 执行USDT转账: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ -> TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA, 金额: 12.52 USDT
2025-08-07 07:10:08,967 [INFO] 📋 使用权限地址: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 07:10:08,967 [INFO] 📋 使用USDT合约: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 07:10:09,998 [INFO] 🔄 使用TronPy执行transferFrom
2025-08-07 07:10:09,998 [INFO]    From: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 07:10:09,998 [INFO]    To: TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA
2025-08-07 07:10:09,999 [INFO]    Amount: 12.52 USDT (12520000 wei)
2025-08-07 07:10:09,999 [INFO]    Contract: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
2025-08-07 07:10:10,231 [INFO] ✅ TronPy转账成功: 60065fb49beacfe3e6b830355ac27500a1e8c71b8f6248a0aa74103ddf219bc2
2025-08-07 07:10:11,952 [INFO] ✅ 交易确认成功: 60065fb49beacfe3e6b830355ac27500a1e8c71b8f6248a0aa74103ddf219bc2
2025-08-07 07:10:11,952 [INFO] ✅ 转账成功: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 金额: 12.52 USDT, 交易哈希: 60065fb49beacfe3e6b830355ac27500a1e8c71b8f6248a0aa74103ddf219bc2
2025-08-07 07:10:11,980 [INFO] ✅ 转账完成: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, 金额: 12.52 USDT
2025-08-07 07:10:12,496 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 07:10:12,496 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 07:33:04,662 [INFO] ✅ 代理服务器可用: http://127.0.0.1:7891
2025-08-07 07:33:04,671 [INFO] 🌐 网络模式: 代理
2025-08-07 07:33:04,672 [INFO] ✅ 使用固定数据库配置
2025-08-07 07:33:04,677 [INFO] 🌐 HTTP服务器已启动，监听端口: 5000
2025-08-07 07:33:04,677 [INFO] 📡 触发接口: POST http://localhost:5000/trigger_check
2025-08-07 07:33:04,678 [INFO] 💚 健康检查: GET http://localhost:5000/health
2025-08-07 07:33:04,678 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 07:33:04,678 [INFO] 🎯 启动定时监控服务 (每分钟执行一次)
2025-08-07 07:33:04,678 [INFO] 🔍 开始执行地址监控任务
2025-08-07 07:33:04,694 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 07:33:04,694 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 07:33:04,717 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 07:33:04,718 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-08-07 07:33:04,718 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 07:33:04,726 [INFO] 📋 监控地址数量: 1
2025-08-07 07:33:04,734 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:33:05,751 [INFO] ✅ 查询成功: USDT=0.0, TRX=45.14358
2025-08-07 07:33:05,767 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 0.0, TRX: 45.14358
2025-08-07 07:33:05,795 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:33:06,818 [INFO] ✅ 查询成功: USDT=0.0, TRX=45.14358
2025-08-07 07:33:06,818 [INFO] 💰 获取最新余额成功: USDT=0.0, TRX=45.14358
2025-08-07 07:33:06,825 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 07:33:06,832 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=0.0, TRX=45.14358
2025-08-07 07:33:07,340 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 07:33:07,340 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 07:34:07,340 [INFO] 🔍 开始执行地址监控任务
2025-08-07 07:34:07,358 [INFO] ⚙️ 系统配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 07:34:07,358 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 07:34:07,375 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 07:34:07,394 [INFO] 📋 监控地址数量: 1
2025-08-07 07:34:07,412 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:34:08,441 [INFO] ✅ 查询成功: USDT=0.0, TRX=45.14358
2025-08-07 07:34:08,466 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 0.0, TRX: 45.14358
2025-08-07 07:34:08,491 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 07:34:09,478 [INFO] ✅ 查询成功: USDT=0.0, TRX=45.14358
2025-08-07 07:34:09,478 [INFO] 💰 获取最新余额成功: USDT=0.0, TRX=45.14358
2025-08-07 07:34:09,483 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 07:34:09,490 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=0.0, TRX=45.14358
2025-08-07 07:34:09,999 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 07:34:09,999 [INFO] ⏰ 等待60秒后进行下次监控...
2025-08-07 10:04:41,683 [INFO] ✅ 代理服务器可用: http://127.0.0.1:7891
2025-08-07 10:04:41,689 [INFO] 🌐 网络模式: 代理
2025-08-07 10:04:41,689 [INFO] ✅ 使用固定数据库配置
2025-08-07 10:04:41,693 [INFO] 🌐 HTTP服务器已启动，监听端口: 6689
2025-08-07 10:04:41,693 [INFO] 📡 触发接口: POST http://localhost:6689/trigger_check
2025-08-07 10:04:41,693 [INFO] 💚 健康检查: GET http://localhost:6689/health
2025-08-07 10:04:41,693 [INFO] 🚀 USDT授权地址监控器启动
2025-08-07 10:04:41,694 [INFO] 🎯 启动定时监控服务
2025-08-07 10:04:41,713 [INFO] 🔍 开始执行地址监控任务
2025-08-07 10:04:41,713 [INFO] ⚙️ 地址监控配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 10:04:41,713 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 10:04:41,737 [INFO] [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:6689
 * Running on http://*************:6689
2025-08-07 10:04:41,738 [INFO] [33mPress CTRL+C to quit[0m
2025-08-07 10:04:41,742 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 10:04:41,759 [INFO] 📋 监控地址数量: 1
2025-08-07 10:04:41,759 [INFO] 🔄 使用顺序处理模式
2025-08-07 10:04:41,933 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 10:04:42,486 [INFO] ✅ 查询成功: USDT=0.0, TRX=42.37558
2025-08-07 10:04:42,510 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 0.0, TRX: 42.37558
2025-08-07 10:04:42,543 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 10:04:47,668 [ERROR] ❌ 获取账户余额失败 TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ: HTTPSConnectionPool(host='api.trongrid.io', port=443): Read timed out. (read timeout=5)
2025-08-07 10:04:47,668 [WARNING] ⚠️ API密钥 1 查询失败
2025-08-07 10:04:47,668 [ERROR] ❌ 所有API密钥都查询失败: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 10:04:47,668 [WARNING] ⚠️ 获取最新余额失败，使用缓存余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 10:04:47,673 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 10:04:47,681 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=0.000000, TRX=0
2025-08-07 10:04:47,798 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 10:04:47,805 [INFO] ⏰ 等待3.1秒后进行下次监控... (API Keys: 1个)
2025-08-07 10:04:50,904 [INFO] 🔍 开始执行地址监控任务
2025-08-07 10:04:50,905 [INFO] ⚙️ 地址监控配置: API密钥数量=1, 监控间隔=3000ms
2025-08-07 10:04:50,905 [INFO] 🔄 开始检查fish表数据完整性...
2025-08-07 10:04:50,924 [INFO] ✅ fish表数据完整性检查完成，无需同步
2025-08-07 10:04:50,941 [INFO] 📋 监控地址数量: 1
2025-08-07 10:04:50,941 [INFO] 🔄 使用顺序处理模式
2025-08-07 10:04:51,078 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 10:04:51,632 [INFO] ✅ 查询成功: USDT=0.0, TRX=42.37558
2025-08-07 10:04:51,657 [INFO] 📊 TJp1dGHZ1S... USDT: 0.000000 -> 0.0, TRX: 42.37558
2025-08-07 10:04:51,674 [INFO] 🔍 开始查询地址余额: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, API密钥数量: 1
2025-08-07 10:04:52,194 [INFO] ✅ 查询成功: USDT=0.0, TRX=42.37558
2025-08-07 10:04:52,194 [INFO] 💰 获取最新余额成功: USDT=0.0, TRX=42.37558
2025-08-07 10:04:52,201 [INFO] 📊 更新fish表记录: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ
2025-08-07 10:04:52,211 [INFO] ✅ 数据已同步到fish表: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ, USDT=0.0, TRX=42.37558
2025-08-07 10:04:52,320 [INFO] ✅ 地址监控任务完成 - 监控地址: 1/1, 鱼苗表同步: 1/1
2025-08-07 10:04:52,337 [INFO] ⏰ 等待3.1秒后进行下次监控... (API Keys: 1个)
