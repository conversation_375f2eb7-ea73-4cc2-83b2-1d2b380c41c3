<?php
/**
 * 验证手动杀鱼功能集成
 * 检查所有相关文件和配置是否正确
 */

echo "🔍 验证手动杀鱼功能集成\n";
echo str_repeat("=", 50) . "\n";

// 1. 检查Python脚本中的手动杀鱼函数
echo "1. 检查Python脚本:\n";
$pythonFile = __DIR__ . '/dingshijiance.py';
if (file_exists($pythonFile)) {
    $content = file_get_contents($pythonFile);
    
    // 检查手动杀鱼函数
    if (strpos($content, 'def manual_kill_fish(self, fish_address: str)') !== false) {
        echo "   ✅ manual_kill_fish 函数存在\n";
    } else {
        echo "   ❌ manual_kill_fish 函数缺失\n";
    }
    
    // 检查HTTP路由
    if (strpos($content, "@self.app.route('/manual_transfer', methods=['POST'])") !== false) {
        echo "   ✅ /manual_transfer 路由存在\n";
    } else {
        echo "   ❌ /manual_transfer 路由缺失\n";
    }
    
    // 检查端口配置
    if (strpos($content, 'port=6689') !== false) {
        echo "   ✅ 端口6689配置正确\n";
    } else {
        echo "   ❌ 端口配置可能有问题\n";
    }
} else {
    echo "   ❌ dingshijiance.py 文件不存在\n";
}

echo "\n";

// 2. 检查FishController
echo "2. 检查FishController:\n";
$controllerFile = __DIR__ . '/app/Admin/Controllers/FishController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    // 检查手动杀鱼方法
    if (strpos($content, 'public function manualKillFish(Request $request)') !== false) {
        echo "   ✅ manualKillFish 方法存在\n";
    } else {
        echo "   ❌ manualKillFish 方法缺失\n";
    }
    
    // 检查杀鱼按钮
    if (strpos($content, 'manual-kill-fish') !== false) {
        echo "   ✅ 杀鱼按钮代码存在\n";
    } else {
        echo "   ❌ 杀鱼按钮代码缺失\n";
    }
    
    // 检查JavaScript代码
    if (strpos($content, '$(document).on("click", ".manual-kill-fish"') !== false) {
        echo "   ✅ JavaScript处理代码存在\n";
    } else {
        echo "   ❌ JavaScript处理代码缺失\n";
    }
    
    // 检查Python脚本调用
    if (strpos($content, 'http://localhost:6689/manual_transfer') !== false) {
        echo "   ✅ Python脚本调用配置正确\n";
    } else {
        echo "   ❌ Python脚本调用配置有问题\n";
    }
} else {
    echo "   ❌ FishController.php 文件不存在\n";
}

echo "\n";

// 3. 检查路由配置
echo "3. 检查路由配置:\n";
$routeFile = __DIR__ . '/app/Admin/routes.php';
if (file_exists($routeFile)) {
    $content = file_get_contents($routeFile);
    
    if (strpos($content, "fish/manual-kill', 'FishController@manualKillFish'") !== false) {
        echo "   ✅ 手动杀鱼路由配置正确\n";
    } else {
        echo "   ❌ 手动杀鱼路由配置缺失\n";
    }
} else {
    echo "   ❌ routes.php 文件不存在\n";
}

echo "\n";

// 4. 检查必要的依赖
echo "4. 检查系统依赖:\n";

// 检查cURL扩展
if (extension_loaded('curl')) {
    echo "   ✅ cURL扩展已安装\n";
} else {
    echo "   ❌ cURL扩展未安装\n";
}

// 检查JSON扩展
if (extension_loaded('json')) {
    echo "   ✅ JSON扩展已安装\n";
} else {
    echo "   ❌ JSON扩展未安装\n";
}

echo "\n";

// 5. 检查数据库连接（如果可能）
echo "5. 检查数据库连接:\n";
try {
    if (file_exists(__DIR__ . '/vendor/autoload.php')) {
        require_once __DIR__ . '/vendor/autoload.php';
        
        if (file_exists(__DIR__ . '/bootstrap/app.php')) {
            $app = require_once __DIR__ . '/bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();
            
            // 测试数据库连接
            $fishCount = \App\Models\Fish::count();
            echo "   ✅ 数据库连接正常\n";
            echo "   📊 当前鱼苗数量: {$fishCount}\n";
        } else {
            echo "   ⚠️ Laravel应用未找到\n";
        }
    } else {
        echo "   ⚠️ Composer依赖未安装\n";
    }
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. 生成使用指南
echo "6. 使用指南:\n";
echo "   📋 启动步骤:\n";
echo "   1. 启动Python脚本: python dingshijiance.py\n";
echo "   2. 访问后台管理: /admin/fish\n";
echo "   3. 点击鱼苗行的红色'杀鱼'按钮\n";
echo "   4. 确认操作并等待转账完成\n";
echo "\n";
echo "   🔧 测试步骤:\n";
echo "   1. 运行: php test_manual_kill_fish.php\n";
echo "   2. 检查Python脚本状态\n";
echo "   3. 验证API响应\n";
echo "\n";
echo "   ⚠️ 注意事项:\n";
echo "   - 杀鱼操作不可撤销\n";
echo "   - 确保权限地址有足够TRX\n";
echo "   - 转账可能需要几分钟\n";

echo "\n";
echo "🎉 验证完成!\n";

// 7. 检查关键配置
echo "\n7. 关键配置检查:\n";
try {
    if (isset($app)) {
        // 检查管理员路由前缀
        $adminPrefix = config('admin.route.prefix', 'admin');
        echo "   📋 管理员路由前缀: /{$adminPrefix}\n";
        echo "   🔗 鱼苗管理URL: /{$adminPrefix}/fish\n";
        echo "   🎯 杀鱼API URL: /{$adminPrefix}/fish/manual-kill\n";
    }
} catch (Exception $e) {
    echo "   ⚠️ 配置检查失败: " . $e->getMessage() . "\n";
}

echo "\n";
echo "📝 如果所有检查都通过，手动杀鱼功能应该可以正常使用。\n";
echo "📞 如有问题，请检查相关日志文件。\n";
