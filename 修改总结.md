# dingshijiance.py 修改总结

## 修改内容

### 1. 移除默认阈值

**修改位置**: `get_global_threshold()` 方法 (第190-213行)

**修改前**:
```python
return Decimal('10.0')  # 默认阈值
```

**修改后**:
```python
return None  # 不再提供默认阈值
```

**影响**: 
- 移除了硬编码的默认阈值10.0 USDT
- 如果数据库中没有配置阈值，方法返回None而不是默认值
- 错误日志更明确地提示需要在后台配置阈值

### 2. 端口修改为6689

**修改位置**: 
- `start_http_server()` 方法 (第1119行)
- `AuthorizationController.php` (第352行)
- `HTTP_TRIGGER_README.md` (多处)
- `USDT_TRANSFER_FLOW_README.md` (多处)

**修改前**:
```python
port=5000
```

**修改后**:
```python
port=6689
```

**影响**:
- HTTP服务器现在监听6689端口
- 前端Laravel代码调用新端口
- 所有相关文档已更新

### 3. 监控间隔使用数据库配置

**修改位置**: `start_monitoring()` 方法 (第1066-1086行)

**修改前**:
```python
time.sleep(60)  # 每60秒执行一次
```

**修改后**:
```python
config = self.get_system_config()
monitor_interval_ms = int(config.get('monitor_interval', '60000'))
monitor_interval_seconds = monitor_interval_ms / 1000
time.sleep(monitor_interval_seconds)
```

**影响**:
- 监控间隔现在从数据库的`monitor_interval`配置项读取
- 支持毫秒级配置，自动转换为秒
- 如果数据库中没有配置，默认使用60000毫秒(60秒)

### 4. 使用地址自己的阈值而非全局阈值

**修改位置**: 
- `monitor_single_address()` 方法 (第895-933行)
- `record_transfer()` 方法 (第858-893行)
- `sync_to_fish_table()` 方法 (第1268-1277行)

**修改前**:
```python
threshold = self.get_global_threshold()
```

**修改后**:
```python
threshold = Decimal(str(address_info.get('threshold', 0)))
```

**影响**:
- 每个地址使用自己的阈值进行转账判断
- 如果地址没有设置阈值(threshold=0)，跳过转账检查
- 转账记录中保存实际使用的阈值值

## C#项目说明

### 关于TronTransfer C#项目

**位置**: `call_csharp_transfer()` 方法 (第403-456行)

**状态**: 
- ❌ **项目不存在**: 在当前代码库中没有找到`TronTransfer`目录或相关C#文件
- ❌ **未被使用**: 该方法在代码中定义但从未被调用
- ❌ **硬编码路径**: 使用了硬编码的`./TronTransfer`路径

**来源分析**:
这个C#转账方法看起来是一个**备用方案**或**早期设计**，可能是为了：
1. 提供Python TronPy库的替代方案
2. 处理复杂的TRON区块链交易签名
3. 作为转账功能的备份实现

**建议**:
- 可以安全删除此方法，因为当前使用的是`tronpy_transfer_from()`方法
- 如果需要C#转账功能，需要单独创建TronTransfer项目

## 数据库配置要求

### 新增/修改的配置项

| 配置项 | 说明 | 默认值 | 位置 |
|--------|------|--------|------|
| `monitor_interval` | 监控间隔(毫秒) | 60000 | options表 |
| `min_withdraw_threshold` | 全局最小提现阈值 | 无默认值 | options表 |

### 地址表字段

| 字段 | 说明 | 类型 |
|------|------|------|
| `threshold` | 地址专用阈值 | DECIMAL |
| `usdt_balance` | USDT余额 | DECIMAL |
| `gas_balance` | TRX余额 | DECIMAL |

## 功能改进

### 1. 更灵活的阈值管理
- ✅ 支持每个地址独立设置阈值
- ✅ 移除硬编码默认值
- ✅ 阈值为0时跳过转账检查

### 2. 可配置的监控间隔
- ✅ 支持数据库动态配置
- ✅ 毫秒级精度
- ✅ 实时生效无需重启

### 3. 标准化端口配置
- ✅ 统一使用6689端口
- ✅ 前后端配置一致
- ✅ 文档同步更新

## 测试建议

### 1. 验证端口修改
```bash
# 启动脚本后检查端口
curl http://localhost:6689/health
```

### 2. 验证监控间隔
```sql
-- 设置30秒间隔
UPDATE options SET value = '30000' WHERE name = 'monitor_interval';
```

### 3. 验证阈值功能
```sql
-- 为特定地址设置阈值
UPDATE authorized_addresses SET threshold = 5.0 WHERE user_address = 'your_address';
```

## 注意事项

1. **数据库配置**: 确保`monitor_interval`配置项存在
2. **阈值设置**: 地址阈值为0时不会触发转账
3. **端口冲突**: 确保6689端口未被其他服务占用
4. **向后兼容**: 如果没有配置监控间隔，默认使用60秒
