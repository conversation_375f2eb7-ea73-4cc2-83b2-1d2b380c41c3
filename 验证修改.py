#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证dingshijiance.py修改的脚本
检查所有修改是否正确实施
"""

import re
import sys

def check_file_modifications():
    """检查文件修改"""
    print("🔍 验证 dingshijiance.py 修改...")
    
    try:
        with open('dingshijiance.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 找不到 dingshijiance.py 文件")
        return False
    
    checks = []
    
    # 1. 检查端口修改
    port_6689_found = 'port=6689' in content
    checks.append(("端口修改为6689", port_6689_found))
    
    # 2. 检查默认阈值移除
    no_default_threshold = 'return Decimal(\'10.0\')' not in content
    checks.append(("移除默认阈值10.0", no_default_threshold))
    
    # 3. 检查监控间隔使用数据库配置
    monitor_interval_db = 'monitor_interval_ms = int(config.get(\'monitor_interval\', \'60000\'))' in content
    checks.append(("监控间隔使用数据库配置", monitor_interval_db))
    
    # 4. 检查使用地址自己的阈值
    address_threshold = 'threshold = Decimal(str(address_info.get(\'threshold\', 0)))' in content
    checks.append(("使用地址自己的阈值", address_threshold))
    
    # 5. 检查record_transfer方法参数修改
    record_transfer_param = 'threshold_used: Decimal = None' in content
    checks.append(("record_transfer方法支持阈值参数", record_transfer_param))
    
    # 6. 检查HTTP服务器日志信息
    http_log_6689 = '监听端口: 6689' in content
    checks.append(("HTTP服务器日志显示6689端口", http_log_6689))
    
    # 输出检查结果
    print("\n📋 修改验证结果:")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有修改验证通过！")
    else:
        print("⚠️ 部分修改验证失败，请检查代码")
    
    return all_passed

def check_frontend_modifications():
    """检查前端修改"""
    print("\n🔍 验证前端修改...")
    
    try:
        with open('app/Http/Controllers/Api/AuthorizationController.php', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 找不到 AuthorizationController.php 文件")
        return False
    
    # 检查端口修改
    port_6689_found = 'localhost:6689/trigger_check' in content
    
    if port_6689_found:
        print("✅ 前端端口修改为6689 - 通过")
        return True
    else:
        print("❌ 前端端口修改失败")
        return False

def check_documentation():
    """检查文档修改"""
    print("\n🔍 验证文档修改...")
    
    docs_to_check = [
        'HTTP_TRIGGER_README.md',
        'USDT_TRANSFER_FLOW_README.md'
    ]
    
    all_docs_ok = True
    
    for doc_file in docs_to_check:
        try:
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if ':6689' in content and ':5000' not in content:
                print(f"✅ {doc_file} 端口更新 - 通过")
            else:
                print(f"❌ {doc_file} 端口更新失败")
                all_docs_ok = False
                
        except FileNotFoundError:
            print(f"⚠️ 找不到文档文件: {doc_file}")
    
    return all_docs_ok

def check_database_compatibility():
    """检查数据库兼容性"""
    print("\n🔍 验证数据库兼容性...")
    
    try:
        with open('database/sql/create_transfer_records_table.sql', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 找不到数据库表结构文件")
        return False
    
    # 检查threshold_value字段
    threshold_field = 'threshold_value' in content and 'decimal(16,6)' in content
    
    if threshold_field:
        print("✅ 数据库表支持threshold_value字段 - 通过")
        return True
    else:
        print("❌ 数据库表缺少threshold_value字段")
        return False

def main():
    """主函数"""
    print("🚀 开始验证 dingshijiance.py 修改...")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        check_file_modifications(),
        check_frontend_modifications(), 
        check_documentation(),
        check_database_compatibility()
    ]
    
    print("\n" + "=" * 60)
    
    if all(checks):
        print("🎉 所有修改验证通过！系统已准备就绪。")
        print("\n📝 下一步操作:")
        print("1. 确保数据库中有 monitor_interval 配置项")
        print("2. 为需要监控的地址设置 threshold 值")
        print("3. 重启 dingshijiance.py 脚本")
        print("4. 测试新的6689端口是否正常工作")
        return True
    else:
        print("❌ 部分修改验证失败，请检查并修复问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
